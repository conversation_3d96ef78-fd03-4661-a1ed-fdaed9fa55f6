.thumbnail-swatch > img {
    border-radius: inherit;
    height: 100%;
  }
  
  :checked + .thumbnail-swatch:before, .thumbnail-swatch.is-selected:before {
    opacity: 1;
    transform: scale(1);
  }
  
  :focus-visible + .thumbnail-swatch {
    outline-offset: 4px;
  }
  
  .thumbnail-swatch--sm {
    --swatch-size: 48px;
  }
  
  .thumbnail-swatch__view-more {
    width: var(--spacing-12);
    height: var(--spacing-12);
    border-radius: min(4px, var(--rounded-input));
    border-width: 1px;
    place-items: center;
    display: grid;
  }
  
  .block-swatch {
    place-items: center;
    gap: var(--spacing-3);
    height: var(--input-height);
    white-space: nowrap;
    border-radius: var(--rounded-button);
    background: rgb(var(--input-background, var(--background))) no-repeat;
    border-width: 1px;
    padding-inline-start: var(--spacing-5);
    padding-inline-end: var(--spacing-5);
    display: flex;
    position: relative;
  }
  
  .block-swatch.is-disabled {
    color: rgb(var(--text-color) / .5);
    background-image: linear-gradient(to bottom right, #0000 50%, currentColor 50% calc(50% + 2px), #0000 calc(50% + 2px));
  }
  
  :disabled + .block-swatch {
    display: none;
  }
  
  .block-swatch:before {
    content: "";
    box-shadow: 0 0 0 2px rgb(var(--text-color));
    border-radius: inherit;
    opacity: 0;
    will-change: transform;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    transform: scale(.95);
  }
  
  :checked + .block-swatch.is-disabled {
    background-image: linear-gradient(to bottom right, transparent 50%, rgb(var(--text-color)) 50% calc(50% + 2px), transparent calc(50% + 2px));
  }
  
  :checked + .block-swatch:before, .block-swatch.is-selected:before {
    opacity: 1;
    transform: scale(1);
  }
  
  :focus-visible + .block-swatch {
    outline-offset: 4px;
  }
  
  .block-swatch__color {
    width: var(--spacing-3-5);
    height: var(--spacing-3-5);
    border-radius: var(--rounded-full);
    background: var(--swatch-background) center / cover;
  }
  
  @media screen and (min-width: 700px) {
    .block-swatch {
      padding-inline-start: var(--spacing-6);
      padding-inline-end: var(--spacing-6);
    }
  }
  
  .image-filter-list {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: .5rem;
    display: grid;
  }
  
  .facet-dialog .image-filter-list {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  
  .image-filter {
    text-align: center;
    border-width: 1px;
    border-radius: 2px;
    flex-direction: column;
    align-items: center;
    row-gap: .5rem;
    padding: 1rem 1rem .75rem;
    transition: border-color .2s ease-in-out, box-shadow .2s ease-in-out;
    display: flex;
  }
  
  :checked + .image-filter, .image-filter.is-selected {
    border-color: currentColor;
    box-shadow: inset 0 0 0 1px;
  }
  
  .image-filter__image {
    width: 2rem;
  }
  
  [data-tooltip] {
    position: relative;
  }
  
  @media screen and (pointer: fine) {
    [data-tooltip]:after {
      content: attr(data-tooltip);
      bottom: calc(100% + var(--spacing-2));
      border-radius: min(24px, var(--rounded-button));
      background: rgb(var(--text-color));
      color: rgb(var(--background));
      visibility: hidden;
      opacity: 0;
      pointer-events: none;
      width: max-content;
      padding: 2px 10px;
      font-size: 12px;
      font-weight: bold;
      transition: opacity .2s ease-in-out, visibility .2s ease-in-out, transform .2s ease-in-out;
      position: absolute;
      left: 50%;
      transform: translateX(-50%)scale(.9);
    }
  
    [data-tooltip]:hover:after {
      visibility: visible;
      opacity: 1;
      will-change: transform;
      transform: translateX(-50%)scale(1);
    }
  }
  
  .lock {
    overflow: hidden;
  }
  
  .text-with-icon {
    gap: var(--spacing-3);
    align-items: center;
    display: flex;
  }
  
  .text-with-icon > svg {
    flex-shrink: 0;
  }
  
  .offer {
    gap: var(--spacing-2);
    padding: var(--spacing-5);
    border-radius: var(--rounded-sm);
    display: grid;
  }
  
  .offer--center {
    text-align: center;
    justify-items: center;
  }
  
  .social-media {
    gap: var(--spacing-3) var(--spacing-4);
    flex-wrap: wrap;
    display: flex;
  }
  
  .social-media--sm svg {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  @media screen and (min-width: 700px) {
    .social-media--sm svg {
      width: 1.7rem;
      height: 1.7rem;
    }
  }
  
  .share-buttons {
    align-items: center;
    gap: var(--spacing-2-5);
    display: flex;
  }
  
  .share-buttons__item {
    width: var(--spacing-8-5);
    height: var(--spacing-8-5);
    opacity: .7;
    background: rgb(var(--text-color) / 0);
    border-radius: var(--rounded-full);
    place-content: center;
    transition: opacity .2s ease-in-out, background .2s ease-in-out;
    display: grid;
  }
  
  .share-buttons__item:hover {
    opacity: 1;
    background: rgb(var(--text-color) / .1);
  }
  
  .floating-controls-container {
    position: relative;
  }
  
  .floating-controls-container .circle-button {
    opacity: 0;
    visibility: hidden;
    z-index: 1;
    transition: opacity .1s ease-in-out, transform .1s ease-in-out, visibility .1s ease-in-out;
    position: absolute;
    top: calc(50% - 24px);
    transform: scale(.8);
  }
  
  .floating-controls-container .circle-button:first-of-type:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--spacing-5);
  }
  
  .floating-controls-container .circle-button:first-of-type:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--spacing-5);
  }
  
  .floating-controls-container .circle-button:last-of-type:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--spacing-5);
  }
  
  .floating-controls-container .circle-button:last-of-type:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--spacing-5);
  }
  
  @media screen and (pointer: fine) {
    .floating-controls-container:hover .is-scrollable ~ .circle-button:not([disabled]) {
      opacity: 1;
      visibility: visible;
      transform: scale(1);
    }
  }
  
  .icon {
    vertical-align: middle;
    display: block;
  }
  
  [dir="rtl"] .reverse-icon {
    transform: rotate(180deg);
  }
  
  .offset-icon {
    --icon-baseline-distance: calc(1em * 1.6);
    --icon-offset: calc((var(--icon-baseline-distance)  - var(--icon-height)) / 2);
    top: var(--icon-offset);
    position: relative;
  }
  
  .icon-chevron-bottom {
    transition: transform .2s ease-in-out;
  }
  
  .group[aria-expanded="true"] > .icon-chevron-bottom {
    transform: rotate(180deg);
  }
  
  .icon-block {
    padding: var(--spacing-6);
    background: rgb(var(--background));
  }
  
  .image-icon {
    width: var(--mobile-icon-max-width, var(--icon-max-width));
  }
  
  @media screen and (min-width: 700px) {
    .image-icon {
      width: var(--icon-max-width);
    }
  }
  
  custom-cursor {
    z-index: 2;
    pointer-events: none;
    opacity: 0;
    visibility: hidden;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    scale: .5;
    transition: opacity .1s, scale .1s, visibility .1s !important;
  }
  
  @media screen and (pointer: fine) {
    custom-cursor {
      display: block;
    }
  
    custom-cursor.is-visible {
      opacity: 1;
      visibility: visible;
      scale: 1;
    }
  }
  
/* :has( > custom-cursor.is-visible:not(.is-scrolling)) {
  cursor: none;
} */
  
  scroll-shadow {
    --scroll-shadow-size: 30px;
  }
  
  split-lines {
    overflow-wrap: anywhere;
  }
  
  product-rerender {
    display: contents;
  }
  
  .customer-form__image {
    display: none;
  }
  
  .customer-form__box {
    padding: var(--spacing-14) var(--container-gutter);
  }
  
  .customer-form__box-inner {
    max-width: 440px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    display: block;
  }
  
  @media screen and (min-width: 700px) {
    .customer-form {
      margin-block-start: var(--spacing-10);
      margin-block-end: var(--spacing-10);
    }
  
    .customer-form__box {
      border-radius: var(--rounded);
      padding-inline-start: var(--spacing-16);
      padding-inline-end: var(--spacing-16);
    }
  }
  
  @media screen and (min-width: 1000px) {
    .customer-form {
      gap: var(--spacing-6);
      grid: auto / auto-flow minmax(0, 680px);
      object-position: center;
      height: 100%;
      display: block;
    }
  }
  
  .account {
    gap: var(--spacing-6);
    max-width: 1350px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    display: grid;
  }
  
  .account-nav__item {
    padding-block-start: var(--spacing-4-5);
    padding-block-end: var(--spacing-4-5);
  }
  
  .account-nav__item[aria-current="page"] {
    border-color: currentColor;
    border-bottom-width: 2px;
  }
  
  .account-header, .account__block-list {
    gap: var(--spacing-6);
    display: grid;
    position: relative;
  }
  
  @media screen and (min-width: 700px) {
    .account {
      gap: var(--spacing-12);
    }
  
    .account-nav__item--logout {
      position: absolute;
    }
  
    .account-nav__item--logout:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      right: 0;
    }
  
    .account-nav__item--logout:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      left: 0;
    }
  
    .account-header--back {
      text-align: center;
      justify-content: center;
    }
  
    .account-header--back > .back-button {
      position: absolute;
    }
  }
  
  .order-table-list {
    display: none;
  }
  
  .order-grid-list {
    gap: var(--spacing-5);
    display: grid;
  }
  
  .order-grid-item {
    gap: var(--spacing-4);
    padding: var(--spacing-6);
    border-width: 1px;
    padding-block-start: var(--spacing-5);
    display: grid;
  }
  
  .order-grid-item__categories {
    gap: var(--spacing-4);
    grid-template-columns: repeat(2, minmax(0, 1fr));
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .order-grid-list {
      display: none;
    }
  
    .order-table-list {
      max-width: 1024px;
      margin-inline-start: auto;
      margin-inline-end: auto;
      display: table;
    }
  
    .order-table-list th, .order-table-list td {
      padding-inline-start: var(--spacing-4);
      padding-inline-end: var(--spacing-4);
    }
  }
  
  .addresses-list {
    --addresses-per-row: 1;
    gap: var(--spacing-5);
    display: grid;
  }
  
  .address {
    gap: var(--spacing-2);
    padding: var(--spacing-6);
    border-width: 1px;
    flex-direction: column;
    width: 100%;
    display: flex;
  }
  
  .address__actions {
    gap: var(--spacing-6);
    margin-block-start: auto;
    padding-block-start: var(--spacing-2);
    display: flex;
  }
  
  .address-form {
    gap: var(--spacing-3);
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .addresses-list {
      --addresses-per-row: 2;
      grid-template-columns: repeat(auto-fit, minmax(0, calc(100% / var(--addresses-per-row)  - var(--spacing-6) / var(--addresses-per-row) * (var(--addresses-per-row)  - 1))));
      gap: var(--spacing-6);
      justify-content: center;
      justify-items: center;
    }
  
    .address {
      padding: var(--spacing-8) var(--spacing-10);
    }
  
    .address-form {
      gap: var(--spacing-6);
    }
  }
  
  @media screen and (min-width: 1000px) {
    .addresses-list {
      --addresses-per-row: 4;
    }
  }
  
  .order {
    align-items: start;
    gap: var(--spacing-6);
    display: grid;
  }
  
  .order-addresses-list {
    gap: var(--spacing-5);
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .order {
      gap: var(--spacing-12);
    }
  
    .order-addresses-list {
      gap: var(--spacing-6);
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  
  @media screen and (min-width: 1150px) {
    .order {
      grid-template-columns: 1fr 380px;
    }
  
    .order-addresses-list {
      grid-template-columns: none;
    }
  }
  
  @media screen and (min-width: 1400px) {
    .order {
      gap: var(--spacing-24);
    }
  }
  
  .announcement-bar {
    padding-block-start: var(--spacing-2-5);
    padding-block-end: var(--spacing-2-5);
  }
  
  .announcement-bar__wrapper {
    gap: var(--spacing-4);
    max-width: 450px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    display: flex;
  }
  
  .announcement-bar__scrolling-list {
    grid: auto / auto-flow max-content;
    justify-content: center;
    display: grid;
    overflow: hidden;
  }
  
  .announcement-bar__static-list {
    text-align: center;
    place-items: center;
    display: grid;
  }
  
  .announcement-bar__static-list > * {
    grid-area: 1 / -1;
  }
  
  .announcement-bar__item {
    grid: auto / auto-flow auto var(--spacing-20);
    place-items: center;
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .announcement-bar {
      padding-block-start: var(--spacing-3-5);
      padding-block-end: var(--spacing-3-5);
    }
  
    .announcement-bar__item {
      grid-auto-columns: auto var(--spacing-40);
    }
  }
  
  @media (prefers-reduced-motion: no-preference) {
    .announcement-bar__item {
      animation: translateFull var(--marquee-animation-duration, 0s) linear infinite;
    }
  }
  
  .article {
    --article-margin-block-end: var(--spacing-14);
  }
  
  .article > .container {
    justify-content: safe center;
    gap: var(--spacing-10);
    margin-block-end: var(--article-margin-block-end);
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .article {
      --article-margin-block-end: var(--spacing-28);
    }
  
    .article > .container {
      gap: var(--spacing-16);
    }
  }
  
  .article-banner {
    grid: var(--article-banner-grid);
    column-gap: var(--article-banner-column-gap);
    align-items: var(--article-banner-horizontal-alignement, center);
    max-width: var(--article-banner-max-width);
    box-sizing: content-box;
    justify-items: center;
    margin-inline-start: auto;
    margin-inline-end: auto;
    padding-block-start: var(--article-banner-padding-block-start, 0);
    display: grid;
    position: relative;
  }
  
  .article-banner:before {
    content: "";
    width: calc((100vw - var(--scrollbar-width, 0px)));
    height: var(--article-banner-before-height);
    pointer-events: none;
    background-color: rgb(var(--article-banner-background));
    position: absolute;
    top: 0;
  }
  
  .article-banner__image {
    z-index: 1;
    width: 100%;
  }
  
  .article-banner__image > img {
    width: 100%;
  }
  
  .article-banner__content {
    gap: var(--spacing-6);
    padding: var(--article-banner-content-padding-block-start) var(--article-banner-content-padding-inline) var(--article-banner-content-padding-block-end);
    justify-items: var(--article-banner-vertical-alignement, center);
    text-align: var(--article-banner-vertical-alignement, center);
    z-index: 2;
    display: grid;
  }
  
  .article-banner__content > .badge {
    --badge-background: var(--article-banner-badge-background);
    padding: var(--spacing-1) var(--spacing-3);
    display: inline-block;
  }
  
  .article__meta {
    justify-content: var(--article-banner-meta-vertical-alignment, center);
    row-gap: var(--spacing-2);
    column-gap: var(--spacing-4);
    flex-wrap: wrap;
    display: flex;
  }
  
  .article__meta .text-with-icon {
    gap: var(--spacing-2);
  }
  
  @media screen and (min-width: 1150px) {
    .article-banner {
      padding-inline-start: var(--container-gutter);
      padding-inline-end: var(--container-gutter);
    }
  
    .article-banner__content {
      grid-area: var(--article-banner-grid-area, content);
    }
  
    .article-banner__image {
      grid-area: var(--article-banner-grid-area, image);
      position: relative;
    }
  
    .article-banner__image:before {
      content: "";
      background: rgb(var(--article-banner-image-overlay));
      border-radius: inherit;
      z-index: 1;
      pointer-events: none;
      transition: background .2s ease-in-out;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
    }
  
    .article__meta {
      column-gap: var(--spacing-6);
    }
  
    .article__meta .text-with-icon {
      gap: var(--spacing-2-5);
    }
  }
  
  .article-content {
    max-width: var(--article-max-width);
    grid-template-columns: minmax(0, 1fr);
    width: 100%;
    margin-inline-start: auto;
    margin-inline-end: auto;
    padding-block-start: var(--spacing-10);
  }
  
  .article-content > .share-buttons .share-buttons__item {
    width: var(--spacing-10);
    height: var(--spacing-10);
    opacity: 1;
  }
  
  @media screen and (min-width: 1000px) {
    .article-content {
      border-bottom-width: 1px;
      padding-block-start: var(--spacing-16);
      padding-block-end: var(--spacing-18);
    }
  }
  
  .article-navigation {
    gap: var(--spacing-6);
    max-width: var(--article-max-width);
    display: grid;
  }
  
  .article-prev-next {
    gap: var(--spacing-5);
    grid: auto / auto-flow 75vw;
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .article-navigation {
      --navigation-margin-block: var(--spacing-16);
      gap: var(--spacing-10);
    }
  
    .article-navigation__title > svg {
      width: 27px;
      height: 24px;
    }
  
    .article-prev-next {
      gap: var(--spacing-12);
      grid: auto / repeat(2, minmax(0, 1fr));
    }
  }
  
  .article-comments {
    --comments-padding-inner: var(--spacing-6);
    gap: var(--spacing-12);
    max-width: var(--article-max-width);
    display: grid;
  }
  
  .comments-list {
    gap: var(--spacing-4);
    padding: var(--comments-padding-inner);
    display: grid;
  }
  
  .comment:not(:first-child) {
    padding-block-start: var(--spacing-4);
  }
  
  .article-comments__form {
    padding: var(--comments-padding-inner);
  }
  
  @media screen and (min-width: 700px) {
    .article-comments {
      --comments-padding-inner: var(--spacing-12);
    }
  
    .comments-list {
      gap: var(--spacing-8);
    }
  
    .comment {
      gap: var(--spacing-6);
      display: flex;
    }
  
    .comment:not(:first-child) {
      padding-block-start: var(--spacing-8);
    }
  
    .comment__gravatar {
      width: var(--spacing-12);
      height: var(--spacing-12);
    }
  }
  
  .before-after {
    --before-after-label-spacing: var(--spacing-5);
    -webkit-user-select: none;
    user-select: none;
    position: relative;
  }
  
  .before-after__label {
    position: absolute;
  }
  
  .before-after__label--left:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--before-after-label-spacing);
  }
  
  .before-after__label--left:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--before-after-label-spacing);
  }
  
  .before-after__label--right:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--before-after-label-spacing);
  }
  
  .before-after__label--right:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--before-after-label-spacing);
  }
  
  .before-after__label--top {
    top: var(--before-after-label-spacing);
  }
  
  .before-after__label--bottom {
    bottom: var(--before-after-label-spacing);
  }
  
  .before-after__after-image {
    clip-path: inset(0 0 0 calc(var(--before-after-initial-drag-position, 50%)  + var(--clip-path-offset, 0px)));
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  [dir="rtl"] .before-after__after-image {
    clip-path: inset(0 calc(var(--before-after-initial-drag-position, 50%)  - var(--clip-path-offset, 0px)) 0 0);
  }
  
  .before-after__cursor-wrapper {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  .before-after__cursor {
    touch-action: none;
    transform: translate(calc(var(--transform-logical-flip) * -50% + var(--clip-path-offset, 0px)), -50%);
    cursor: grab;
    filter: drop-shadow(0 1px 2px #0000001a) drop-shadow(0 1px 1px #0000000f);
    will-change: transform;
    place-items: center;
    width: max-content;
    height: 100%;
    display: grid;
    position: relative;
  }
  
  .before-after__cursor:before {
    content: "";
    width: var(--spacing-0-5);
    background: rgb(var(--background));
    z-index: -1;
    height: 100%;
    position: absolute;
  }
  
  .before-after__cursor {
    top: 50%;
  }
  
  .before-after__cursor:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--before-after-initial-drag-position, 0px);
  }
  
  .before-after__cursor:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--before-after-initial-drag-position, 0px);
  }
  
  .before-after__cursor:active {
    cursor: grabbing;
  }
  
  @media screen and (min-width: 700px) {
    .before-after {
      --before-after-label-spacing: var(--spacing-8);
    }
  
    .before-after__cursor svg {
      width: var(--spacing-10);
      height: var(--spacing-10);
    }
  }
  
  .blog-posts__container {
    gap: var(--spacing-8);
    margin-block-start: var(--spacing-10);
    margin-block-end: var(--spacing-14);
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .blog-posts__container {
      gap: var(--spacing-10);
    }
  }
  
  @media screen and (min-width: 1400px) {
    .blog-posts__container {
      gap: var(--spacing-20);
      margin-block-start: var(--spacing-20);
      margin-block-end: var(--spacing-28);
    }
  }
  
  .blog-banner {
    --banner-container-gap: var(--spacing-16);
    padding-block-start: var(--banner-spacing-block-added, 0px);
  }
  
  .blog-banner-content {
    padding-block-start: var(--banner-content-padding-block-start);
    padding-block-end: var(--spacing-16);
  }
  
  .blog-filter-list {
    --filter-list-item-padding: var(--spacing-2-5) var(--spacing-5);
    white-space: nowrap;
  }
  
  .blog-filter-list [aria-selected="true"] {
    background-color: rgb(var(--background-primary));
    color: rgb(var(--text-primary));
  }
  
  .blog-filter-list [aria-selected="false"] {
    opacity: .5;
    transition: opacity .2s ease-in-out;
  }
  
  @media screen and (pointer: fine) {
    .blog-filter-list [aria-selected="false"]:hover {
      opacity: 1;
    }
  }
  
  .blog-filter-list a {
    padding: var(--filter-list-item-padding);
    display: block;
  }
  
  .blog-banner__form {
    width: 100%;
    max-width: 500px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    padding-inline-start: var(--spacing-6);
    padding-inline-end: var(--spacing-6);
  }
  
  @media screen and (min-width: 700px) {
    .blog-banner {
      --banner-container-gap: var(--spacing-28);
    }
  
    .blog-banner-content {
      padding-block-end: var(--spacing-28);
    }
  
    .blog-banner__form {
      box-sizing: content-box;
    }
  
    .blog-filter-list {
      --filter-list-item-padding: var(--spacing-4) var(--spacing-6);
    }
  }
  
  .blog-posts {
    --blog-articles-gap: var(--spacing-8);
    align-items: flex-start;
    gap: var(--blog-articles-gap);
    display: grid;
  }
  
  .blog-posts-newsletter {
    padding: var(--spacing-8);
    align-content: flex-start;
  }
  
  .blog-posts-newsletter > .form {
    gap: var(--spacing-2);
  }
  
  @media screen and (min-width: 700px) {
    .blog-posts {
      --article-per-row: var(--blog-posts-per-row, 2);
      --blog-articles-gap: var(--spacing-10);
      --blog-articles-margin-block: var(--spacing-14);
      grid: auto / repeat(var(--article-per-row), minmax(0, 1fr));
    }
  
    .blog-posts-newsletter {
      padding: var(--spacing-10);
    }
  
    .blog-posts-newsletter > .form {
      gap: var(--spacing-4);
    }
  
    .blog-posts-newsletter__content > svg {
      width: var(--spacing-8);
      height: var(--spacing-8);
    }
  }
  
  @media screen and (min-width: 1150px) {
    .blog-posts {
      --article-per-row: var(--blog-posts-per-row, 3);
    }
  }
  
  @media screen and (min-width: 1400px) {
    .blog-posts {
      --blog-articles-gap: var(--spacing-20);
    }
  }
  
  .cart {
    gap: var(--section-stack-spacing-block);
    max-width: 1350px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    display: grid;
  }
  
  .cart-header {
    gap: var(--spacing-6);
    text-align: center;
    justify-items: center;
    display: grid;
  }
  
  .cart-header .free-shipping-bar {
    max-width: 450px;
  }
  
  .cart-order {
    align-items: start;
    gap: var(--spacing-6);
    display: grid;
  }
  
  .cart-form {
    gap: var(--spacing-5);
    padding: var(--spacing-6);
    border-width: 1px;
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .cart-order {
      gap: var(--spacing-12);
    }
  
    .cart-form {
      gap: var(--spacing-6);
      padding: var(--spacing-10) var(--spacing-12);
    }
  }
  
  @media screen and (min-width: 1150px) {
    .cart-order {
      grid-template-columns: 1fr 380px;
    }
  
    .cart-order__recap {
      top: calc(var(--sticky-area-height)  + 20px);
      position: sticky;
    }
  }
  
  @media screen and (min-width: 1400px) {
    .cart-order {
      gap: var(--spacing-24);
    }
  }
  
  .free-shipping-bar {
    gap: var(--spacing-2);
    width: 100%;
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .free-shipping-bar {
      gap: var(--spacing-4);
    }
  }
  
  .shipping-estimator {
    gap: var(--spacing-6);
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .shipping-estimator__form {
      flex-wrap: wrap;
      margin-inline-end: var(--spacing-6);
      display: flex;
    }
  }
  
  .cart-drawer {
    --drawer-footer-padding: 1rem 1.5rem 1.5rem 1.5rem;
    --drawer-content-max-height: none;
    height: 100%;
    top: 0;
  }
  
  .cart-drawer::part(outside-close-button) {
    display: none;
  }
  
  .cart-drawer > [is="close-button"] {
    display: grid;
  }
  
  .cart-drawer__top {
    gap: var(--spacing-2);
    padding-block-end: var(--spacing-6);
    display: grid;
  }
  
  .cart-drawer__line-items > * + * {
    padding-block-start: var(--spacing-4);
  }
  
  .cart-drawer__note {
    visibility: hidden;
    width: 100%;
    transition: visibility .2s ease-in-out;
    position: absolute;
    bottom: calc(100% + 1px);
    overflow: hidden;
  }
  
  .cart-drawer__note:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }
  
  .cart-drawer__note:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }
  
  .cart-drawer__note-inner {
    padding: var(--spacing-5);
    background: rgb(var(--dialog-background));
    border-block-start-width: 1px;
    transition: transform .2s ease-in-out;
    transform: translateY(100%);
  }
  
  .cart-drawer__note[open] {
    visibility: visible;
  }
  
  .cart-drawer__note[open] > .cart-drawer__note-inner {
    transform: translateY(0);
  }
  
  .cart-drawer__recommendations {
    --horizontal-product-width: 390px;
  }
  
  @media screen and (min-width: 700px) {
    .cart-drawer {
      --drawer-footer-padding: 1.5rem 2.5rem 2.5rem 2.5rem;
    }
  
    .cart-drawer .free-shipping-bar {
      gap: var(--spacing-2-5);
    }
  
    .cart-drawer .horizontal-product-list-carousel {
      grid-template-columns: none;
    }
  
    .cart-drawer__top {
      gap: var(--spacing-4);
      padding-block-end: var(--spacing-8);
    }
  
    .cart-drawer__line-items > * + * {
      padding-block-start: var(--spacing-6);
    }
  
    .cart-drawer__note-inner {
      padding: var(--spacing-8) var(--spacing-10) var(--spacing-10) var(--spacing-10);
    }
  }
  
  .collection {
    --collection-sidebar-width: 250px;
    gap: var(--spacing-8);
    grid-template-columns: minmax(0, 1fr);
    margin-block-start: var(--spacing-8);
    margin-block-end: var(--section-outer-spacing-block);
    display: grid;
  }
  
  .collection__top-bar, .collection__facets {
    display: none;
  }
  
  .collection__results {
    scroll-padding-top: calc(var(--sticky-area-height)  + 20px);
    transition: opacity .1s;
  }
  
  .is-loading .collection__results {
    opacity: .4;
  }
  
  @media screen and (min-width: 700px) {
    .collection {
      margin-block-start: var(--spacing-12);
    }
  }
  
  @media screen and (min-width: 1000px) {
    .collection {
      column-gap: var(--spacing-12);
    }
  
    .collection--filters-sidebar {
      grid-template-columns: var(--collection-sidebar-width) minmax(0, 1fr);
    }
  
    .collection__top-bar {
      column-gap: var(--spacing-6);
      grid-column: 1 / -1;
      grid-template-columns: auto minmax(0, 1fr);
      display: grid;
    }
  
    .collection--filters-sidebar .collection__top-bar {
      grid-template-columns: inherit;
      column-gap: inherit;
    }
  
    .collection--filters-horizontal .collection__top-bar {
      row-gap: var(--spacing-8);
      grid-template-columns: 100%;
    }
  
    .collection__facets {
      display: block;
    }
  
    .facets-summary {
      align-items: start;
      gap: var(--spacing-4);
      grid-column-end: -1;
      display: flex;
    }
  
    .collection__facets-scroller {
      display: block;
      position: sticky;
      top: calc(var(--sticky-area-height)  + 20px);
    }
  
    .collection__pagination {
      grid-column-end: -1;
    }
  
    .availability-facet {
      align-items: center;
      gap: var(--spacing-4);
      display: flex;
    }
  
    .collection--filters-horizontal .availability-facet, .collection--filters-horizontal .sort-by-facet {
      flex: 1 0 0;
      min-width: max-content;
    }
  }
  
  @media screen and (min-width: 1400px) {
    .collection {
      --collection-sidebar-width: 300px;
    }
  }
  
  .sort-by-facet {
    justify-content: end;
    gap: var(--spacing-1);
    flex-shrink: 0;
    margin-inline-start: auto;
    display: flex;
    position: relative;
  }
  
  .facets__floating-filter {
    text-align: center;
    z-index: 2;
    width: 100%;
    position: fixed;
    bottom: var(--spacing-8);
  }
  
  .facets__floating-filter:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }
  
  .facets__floating-filter:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }
  
  .facets-drawer::part(header) {
    padding-block: var(--spacing-6);
  }
  
  .facets-drawer::part(body) {
    flex-grow: 1;
    padding-block-start: 0;
    padding-block-end: 0;
  }
  
  .facets-drawer .facets-vertical {
    flex-direction: column;
    min-height: 100%;
    display: flex;
  }
  
  .facets-drawer .accordion {
    border-block-start-width: 0;
  }
  
  .facets-drawer__floating-apply {
    background: linear-gradient(180deg, rgb(var(--background-primary) / 0), rgb(var(--background)));
    margin-block-start: auto;
    margin-inline-start: calc(-1 * var(--spacing-3));
    margin-inline-end: calc(-1 * var(--spacing-3));
    padding-block-start: var(--spacing-5);
    padding-block-end: var(--spacing-5);
    position: sticky;
    bottom: 0;
  }
  
  @media screen and (max-width: 699px) {
    .facets-drawer::part(header) {
      display: none;
    }
  
    .facets-drawer::part(content) {
      height: 65vh;
      max-height: none;
    }
  }
  
  @media screen and (min-width: 700px) {
    .facets-drawer__floating-apply {
      margin-inline-start: calc(-1 * var(--spacing-4));
      margin-inline-end: calc(-1 * var(--spacing-4));
      padding-block-start: var(--spacing-6);
      padding-block-end: var(--spacing-6);
    }
  }
  
  .facets-horizontal {
    justify-content: center;
    align-items: center;
    gap: var(--spacing-5) var(--spacing-10);
    flex-wrap: wrap;
    display: flex;
    position: relative;
  }
  
  .facets-horizontal > [aria-expanded] {
    transition: opacity .2s ease-in-out;
  }
  
  @supports selector(:has(*)) {
    .facets-horizontal > [aria-expanded="true"] ~ [aria-expanded] {
      opacity: .5;
    }
  
    .facets-horizontal > :has( ~ [aria-expanded="true"]) {
      opacity: .5;
    }
  }
  
  .active-facets {
    align-items: center;
    gap: var(--spacing-2);
    flex-wrap: wrap;
    display: flex;
  }
  
  .active-facets > .facet-clear-all {
    margin-inline-start: var(--spacing-2);
  }
  
  @media screen and (min-width: 1000px) {
    .active-facets {
      margin-block-start: -12px;
      margin-block-end: -12px;
    }
  
    .active-facets > .facet-clear-all {
      margin-inline-start: var(--spacing-4);
    }
  
    .collection--filters-horizontal .active-facets {
      margin-block-start: 0;
      margin-block-end: 0;
    }
  }
  
  .removable-facet {
    gap: var(--spacing-3);
    padding: var(--spacing-2-5) var(--spacing-4);
    background: rgb(var(--text-color) / .1);
    color: rgb(var(--text-color));
    border-radius: var(--rounded-button);
    flex-shrink: 0;
    align-items: baseline;
    display: flex;
  }
  
  @media screen and (min-width: 700px) {
    .removable-facet {
      padding: var(--spacing-3) var(--spacing-5);
      gap: var(--spacing-4);
    }
  }
  
  .facet-dialog {
    padding: var(--spacing-4);
    border-radius: min(8px, var(--rounded-button));
    background: rgb(var(--background-primary));
    visibility: hidden;
    opacity: 0;
    z-index: 1;
    border-width: 1px;
    width: max-content;
    max-width: min(1100px, 80vw);
    transition: opacity .2s ease-in-out, visibility .2s ease-in-out;
    position: absolute;
    top: calc(100% + var(--spacing-6));
  }
  
  .facet-dialog:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }
  
  .facet-dialog:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }
  
  .facet-dialog > .color-list {
    margin: var(--spacing-1) var(--spacing-2);
    max-width: 550px;
  }
  
  .availability-facet + .facets-horizontal > .facet-dialog:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: auto;
  }
  
  .availability-facet + .facets-horizontal > .facet-dialog:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: auto;
  }
  
  .facet-dialog[open] {
    visibility: visible;
    opacity: 1;
  }
  
  .facet-dialog-option {
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--rounded-full);
    opacity: .7;
    transition: opacity .2s ease-in-out, background .2s ease-in-out;
    display: block;
  }
  
  :checked + .facet-dialog-option, .facet-dialog-option:hover {
    background: rgb(var(--text-primary) / .04);
    opacity: 1;
  }
  
  .collection-header {
    text-align: center;
    margin-block-start: var(--spacing-8);
    margin-block-end: var(--spacing-8);
  }
  
  @media screen and (min-width: 700px) {
    .collection-header {
      margin-block-start: var(--spacing-16);
      margin-block-end: var(--spacing-16);
    }
  }
  
  .collection-featured-product {
    max-width: 290px;
    display: grid;
  }
  
  .collection-featured-product > * {
    background: rgb(var(--text-primary) / .2);
    -webkit-backdrop-filter: blur(80px);
    backdrop-filter: blur(80px);
  }
  
  .collection-featured-product__title {
    padding: var(--spacing-1-5) var(--spacing-4);
    border-top-left-radius: var(--rounded-xs);
    border-top-right-radius: var(--rounded-xs);
    justify-self: end;
  }
  
  .collection-featured-product__content {
    border-radius: var(--rounded-xs);
    border-top-right-radius: 0;
  }
  
  .collection-list {
    grid: var(--collection-list-grid, none);
    align-items: start;
    gap: var(--grid-gutter);
    display: grid;
  }
  
  .collection-card {
    --transition-direction: 1;
    scroll-snap-align: center;
    scroll-snap-stop: always;
    overflow: hidden;
  }
  
  .collection-card--reverse-transition {
    --transition-direction: -1;
  }
  
  .collection-card__content-wrapper svg {
    opacity: 0;
    visibility: hidden;
    transition: opacity .2s, visibility .2s, transform .2s;
    display: inline-block;
  }
  
  .collection-card__content + svg {
    transform: translateY(calc(var(--transition-direction) * 48px));
  }
  
  .collection-card__content {
    transition: transform .2s;
  }
  
  .collection-card__content:first-child {
    margin-block-end: -3rem;
  }
  
  @media screen and (min-width: 1000px) {
    .collection-card {
      scroll-snap-align: none;
    }
  }
  
  @media screen and (pointer: fine) {
    .collection-card:hover .collection-card__content {
      transform: translateY(calc(var(--transition-direction) * -8px));
    }
  
    .collection-card:hover .collection-card__content-wrapper svg {
      opacity: 1;
      visibility: visible;
      transform: translateY(-8px);
    }
  
    .collection-card:hover .collection-card__content + svg {
      transform: translateY(56px);
    }
  }
  
  @supports selector(:has(.selector)) {
    .shopify-section--collection-banner + .shopify-section--collection-list:not(:has(.section-header)) {
      --section-outer-spacing-block: var(--grid-gutter);
    }
  }
  
  @supports not selector(:has(.selector)) {
    .shopify-section--collection-banner + .shopify-section--collection-list {
      --section-outer-spacing-block: var(--grid-gutter);
    }
  }
  
  .contact-form {
    padding: var(--spacing-6);
  }
  
  @media screen and (min-width: 700px) {
    .contact-form {
      padding: var(--spacing-10) var(--spacing-12);
    }
  }
  
  .faq-availability {
    gap: var(--spacing-6);
    text-align: center;
    justify-items: center;
    display: grid;
  }
  
  .faq-availability > * {
    justify-items: inherit;
  }
  
  .section-stack__intro .faq-availability {
    text-align: start;
    justify-items: start;
  }
  
  .feature-chart {
    padding: 0 var(--container-gutter);
    scroll-padding-top: var(--sticky-area-height);
    display: block;
  }
  
  .feature-chart__table-row {
    --feature-chart-heading-width: 140px;
    grid-template-columns: var(--feature-chart-heading-width) repeat(var(--feature-chart-values-columns-count), minmax(140px, 1fr));
    gap: var(--spacing-1) var(--spacing-5);
    min-width: min-content;
    padding-block-start: var(--spacing-4);
    padding-block-end: var(--spacing-4);
    display: grid;
  }
  
  .feature-chart__toggle {
    text-align: center;
    border-top-width: 1px;
    justify-content: center;
    padding-block-start: var(--spacing-4);
    padding-block-end: var(--spacing-4);
    display: grid;
  }
  
  .feature-chart.is-expanded .feature-chart__toggle .circle-chevron {
    transform: rotate(180deg);
  }
  
  .feature-chart__product {
    --feature-chart-product-image-width: 80px;
    gap: var(--spacing-5);
    place-content: start;
    display: grid;
    position: relative;
  }
  
  .feature-chart__product :is(img, svg) {
    width: var(--feature-chart-product-image-width);
  }
  
  .feature-chart__product .color-swatch {
    margin: 0;
  }
  
  .feature-chart__product .badge {
    position: absolute;
    top: 0;
  }
  
  .feature-chart__product .badge:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }
  
  .feature-chart__product .badge:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }
  
  .feature-chart__product-placeholder {
    height: auto;
  }
  
  .feature-chart__table-row--sticky {
    visibility: hidden;
    opacity: 0;
    z-index: 1;
    background: rgb(var(--background));
    padding-block: var(--spacing-5);
    border-bottom-width: 1px;
    transition: opacity .2s, visibility .2s, top .2s;
    position: fixed;
    top: calc(var(--header-is-visible, 1) * var(--sticky-area-height));
    border-top-width: 0 !important;
  }
  
  .feature-chart__table-row--sticky.is-visible {
    opacity: 1;
    visibility: visible;
  }
  
  .feature-chart__table-row--sticky .feature-chart__product img {
    max-width: 48px;
  }
  
  .feature-chart__table-row--sticky .feature-chart__view-button-container {
    display: none;
  }
  
  @media screen and (max-width: 699px) {
    .feature-chart, .feature-chart__table {
      margin-inline-start: calc(-1 * var(--container-gutter));
      margin-inline-end: calc(-1 * var(--container-gutter));
      padding-inline-start: var(--container-gutter);
      padding-inline-end: var(--container-gutter);
      display: grid;
    }
  
    .feature-chart__table-row {
      grid-template-columns: repeat(var(--feature-chart-values-columns-count), minmax(140px, 1fr));
    }
  
    .feature-chart__table--multi-columns .feature-chart__table-row {
      grid-template-columns: repeat(var(--feature-chart-values-columns-count), 150px);
    }
  
    .feature-chart__heading {
      position: sticky;
    }
  
    .feature-chart__heading:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      left: 0;
    }
  
    .feature-chart__heading:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      right: 0;
    }
  
    .feature-chart__value {
      grid-row: 2;
    }
  }
  
  @media screen and (min-width: 700px) {
    .feature-chart {
      border-radius: var(--rounded);
    }
  
    .feature-chart__table-row--product > :first-child {
      grid-column-start: 2;
    }
  }
  
  @media screen and (min-width: 1000px) {
    .feature-chart {
      padding: var(--spacing-4) var(--spacing-12);
    }
  
    .feature-chart__table-row {
      --feature-chart-heading-width: 180px;
      column-gap: var(--spacing-6);
      padding-block-start: var(--spacing-6);
      padding-block-end: var(--spacing-6);
    }
  
    .feature-chart__table-row--product {
      padding-block-end: var(--spacing-10);
    }
  
    .feature-chart__table-row--sticky {
      padding-block: var(--spacing-6);
    }
  
    .feature-chart__toggle {
      padding-block-start: var(--spacing-5);
      padding-block-end: var(--spacing-6);
    }
  
    .feature-chart__product {
      --feature-chart-product-image-width: 150px;
      max-width: 260px;
    }
  
    .feature-chart__table-row--sticky .feature-chart__product {
      grid-auto-flow: column;
      justify-content: start;
    }
  
    .feature-chart__table-row--sticky .feature-chart__product img {
      max-width: 80px;
    }
  }
  
  .footer {
    --background: var(--footer-background);
    --text-color: var(--footer-text);
    --footer-padding-block: var(--spacing-12);
    --footer-part-gap: var(--spacing-10);
    --footer-block-list-gap: var(--spacing-10);
    --footer-block-gap: var(--spacing-4);
    --footer-aside-gap: var(--spacing-10);
    background: rgb(var(--background));
    color: rgb(var(--text-color));
    padding-block-start: var(--footer-padding-block);
    padding-block-end: var(--footer-padding-block);
  }
  
  .footer__wrapper {
    gap: var(--footer-part-gap);
    display: grid;
  }
  
  .footer__block-list {
    gap: var(--footer-block-list-gap);
    align-items: start;
    justify-content: var(--footer-block-list-justify-content, start);
    grid: auto-flow dense / repeat(2, minmax(0, 1fr));
    display: grid;
  }
  
  .footer__block {
    gap: var(--footer-block-gap);
    display: grid;
  }
  
  .footer__block--text, .footer__block--newsletter {
    grid-column: span 2;
  }
  
  .footer__block--newsletter {
    gap: var(--spacing-8);
    display: grid;
  }
  
  .footer__block--menu {
    max-width: 250px;
  }
  
  .footer__aside {
    gap: var(--footer-aside-gap);
    display: grid;
  }
  
  .footer__aside-top, .footer__aside-bottom {
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-8);
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .footer {
      --footer-padding-block: var(--spacing-16);
      --footer-part-gap: var(--spacing-16);
      --footer-block-list-gap: var(--spacing-10) var(--spacing-24);
      --footer-block-gap: var(--spacing-6);
      --footer-aside-gap: var(--spacing-12);
    }
  
    .footer__block-list {
      flex-wrap: wrap;
      display: flex;
    }
  
    .footer__block--text {
      flex-basis: 25%;
    }
  
    .footer__block--newsletter {
      flex-basis: 100%;
    }
  
    .footer__block--menu {
      flex-shrink: 0;
    }
  
    .footer__aside-top, .footer__aside-bottom {
      display: flex;
    }
  
    .footer__copyright {
      order: -1;
    }
  }
  
  @media screen and (min-width: 1000px) {
    .footer {
      --footer-block-list-gap: var(--spacing-10) var(--spacing-12);
    }
  
    .footer__block-list {
      flex-wrap: nowrap;
    }
  
    .footer__block--newsletter {
      flex-basis: 33.3333%;
    }
  
    .footer__block--newsletter:only-child {
      flex-basis: 50%;
    }
  
    .footer__newsletter-form {
      max-width: 400px;
    }
  }
  
  @media screen and (min-width: 1150px) {
    .footer {
      --footer-block-list-gap: var(--spacing-10) var(--spacing-24);
    }
  
    .footer__block--menu:last-child {
      margin-inline-end: var(--spacing-10);
    }
  }
  
  .shopify-section--privacy-banner {
    z-index: 2;
    position: relative;
  }
  
  .privacy-bar {
    margin: var(--spacing-2);
    z-index: 1;
    transition: opacity .2s ease-in-out, visibility .2s ease-in-out;
    position: fixed;
    bottom: 0;
    display: block !important;
  }
  
  .privacy-bar:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }
  
  .privacy-bar:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }
  
  .privacy-bar[hidden] {
    visibility: hidden;
    opacity: 0;
  }
  
  .privacy-bar__inner {
    padding: var(--spacing-6);
    background: rgb(var(--background-primary));
    color: rgb(var(--text-primary));
    border-radius: var(--rounded-sm);
    border-width: 1px;
    width: 100%;
  }
  
  .privacy-bar__close {
    position: absolute;
    top: var(--spacing-6);
  }
  
  .privacy-bar__close:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--spacing-6);
  }
  
  .privacy-bar__close:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--spacing-6);
  }
  
  @media screen and (min-width: 700px) {
    .privacy-bar {
      margin: var(--spacing-4);
    }
  
    .privacy-bar__inner {
      max-width: 445px;
    }
  }
  
  .gift-card {
    gap: var(--spacing-8);
    padding-block-start: var(--spacing-4);
    padding-block-end: var(--spacing-14);
    display: grid;
  }
  
  .gift-card__image-wrapper {
    justify-items: center;
    gap: var(--spacing-6);
    display: grid;
  }
  
  .gift-card__image {
    max-width: 160px;
  }
  
  .gift-card__info {
    gap: var(--spacing-5);
    display: grid;
  }
  
  .gift-card__issued-info {
    place-content: center;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-6);
    text-align: center;
    display: grid;
  }
  
  .gift-card__balance {
    line-height: 1;
    font-size: var(--gift-card-balance-font-size);
  }
  
  .gift-card__redeem-info:last-child {
    border-width: 1px;
  }
  
  .gift-card__redeem-box {
    padding: var(--spacing-6);
    align-items: center;
    gap: var(--spacing-4);
    display: grid;
  }
  
  .gift-card__redeem-box:last-child:not(:only-child) {
    border-top-width: 1px;
  }
  
  .gift-card__redeem-code {
    padding: var(--spacing-3-5);
    text-align: center;
    border-radius: var(--rounded-xs);
    border-width: 1px;
    font-weight: bold;
  }
  
  .gift-card__qr-code {
    aspect-ratio: 1;
    width: 120px;
    display: block;
  }
  
  .gift-card__qr-code > canvas {
    display: none;
  }
  
  .gift-card__scan {
    justify-content: center;
    align-items: center;
    gap: var(--spacing-8);
    display: flex;
  }
  
  @media screen and (min-width: 700px) {
    .gift-card {
      gap: var(--spacing-12);
      padding-block-start: var(--spacing-12);
      padding-block-end: var(--spacing-32);
    }
  
    .gift-card__image-wrapper {
      gap: var(--spacing-12);
    }
  
    .gift-card__image {
      max-width: 270px;
    }
  
    .gift-card__info {
      gap: var(--spacing-12);
    }
  
    .gift-card__issued-info {
      padding: var(--spacing-12);
    }
  
    .gift-card__redeem-info {
      display: flex;
    }
  
    .gift-card__redeem-box {
      padding: var(--spacing-12);
      gap: var(--spacing-6);
      max-width: 380px;
    }
  
    .gift-card__scan {
      justify-items: center;
      display: grid;
    }
  }
  
  @media screen and (min-width: 1000px) {
    .gift-card__info {
      justify-content: center;
      display: flex;
    }
  
    .gift-card__redeem-box:last-child:not(:only-child) {
      border-top-width: 0;
      border-inline-start-width: 1px;
    }
  }
  
  @media screen and (min-width: 1150px) {
    .gift-card__issued-info {
      flex-basis: var(--spacing-80);
    }
  }
  
  .header {
    --background: var(--header-background);
    --text-color: var(--header-text);
    --header-part-gap: var(--spacing-3);
    --header-icon-list-spacing: var(--spacing-1);
    --header-link-list-spacing: var(--spacing-6);
    --header-logo-opacity: 1;
    will-change: transform;
    margin-inline-start: auto;
    margin-inline-end: auto;
    transition: background .25s;
    display: block;
  }
  
  .header__wrapper {
    grid-template: var(--header-grid-template);
    align-items: center;
    gap: var(--header-part-gap);
    padding: var(--header-padding-block) max(var(--container-gutter), 50% - var(--container-max-width) / 2);
    color: rgb(var(--text-color));
    background: rgb(var(--background) / var(--header-background-opacity));
    -webkit-backdrop-filter: blur(var(--header-background-blur-radius));
    backdrop-filter: blur(var(--header-background-blur-radius));
    border-radius: inherit;
    transition: inherit;
    display: grid;
  }
  
  .header__logo {
    grid-area: logo;
    max-width: max-content;
    display: block;
    position: relative;
  }
  
  @media screen and (max-width: 700px) {
    .header__logo {
      margin-top: 3px;
    }
  }
  
  @media screen and (min-width: 700px) {
    .header__logo {
      margin-bottom: 3px;
    }
  }
  
  .header__logo-image {
    width: var(--header-logo-width);
    opacity: var(--header-logo-opacity, 1);
    height: auto;
  }
  
  .header__logo-image--transparent {
    opacity: calc(1 - var(--header-logo-opacity));
    position: absolute;
    top: 0;
  }
  
  .header__logo-image--transparent:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }
  
  .header__logo-image--transparent:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }
  
  .header__logo-image:not(.header__logo-image--transparent):last-child {
    --header-logo-opacity: 1;
  }
  
  .header__logo-text {
    max-width: min(50vw, 350px);
    display: block;
  }
  
  .header__main-nav {
    grid-area: main-nav;
  }
  
  .header__secondary-nav {
    justify-content: flex-end;
    justify-self: end;
    align-items: center;
    gap: var(--spacing-3) var(--header-part-gap);
    flex-wrap: wrap;
    grid-area: secondary-nav;
    display: flex;
  }
  
  .header__link-list {
    display: none;
  }
  
  .header__link-list .icon-chevron-bottom {
    position: relative;
    top: 1px;
  }
  
  .header__icon-list {
    align-items: center;
    gap: var(--header-icon-list-spacing);
    display: flex;
    justify-content: center;
  }
  .header__link-list a.link-faded-reverse {
    font-weight: 600;
    font-size: 16.2px;
    line-height: 25.92px;
  }
  
  .header__cart-count {
    top: -.3125rem;
    position: absolute;
  }
  
  .header__cart-count:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: .875rem;
  }
  
  .header__cart-count:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: .875rem;
  }
  
  @media screen and (min-width: 700px) {
    .header {
      --header-part-gap: var(--spacing-6);
      --header-icon-list-spacing: var(--spacing-6);
    }
  }
  
  @media screen and (min-width: 1150px) {
    .header__link-list {
      gap: var(--spacing-4) var(--header-link-list-spacing);
      display: flex;
    }
  }
  
  @media screen and (min-width: 1400px) {
    .header {
      --header-part-gap: var(--spacing-10);
      --header-link-list-spacing: var(--spacing-10);
    }
  }
  
  .header[allow-transparency] {
    transition: color .25s, background .25s, -webkit-backdrop-filter .25s, backdrop-filter .25s;
  }
  
  .header[allow-transparency]:not(.is-filled) {
    --header-background-opacity: 0;
    --header-logo-opacity: 0;
    --header-background-blur-radius: 0px;
    --text-color: var(--header-transparent-text-color);
  }
  
  .header[allow-transparency] .header__logo-image {
    transition: opacity .25s;
  }
  
  @supports selector(:has(> *)) {
    :has(.shopify-section:first-child [allow-transparent-header]) .header:not(.is-filled) {
      --header-background-opacity: 0;
      --header-logo-opacity: 0;
      --text-color: var(--header-transparent-text-color);
    }
  
    :has(.shopify-section:first-child [allow-transparent-header]) .shopify-section--header:not(:has(.header.is-filled)) {
      position: relative !important;
      top: 0 !important;
    }
  
    .header:has([open]) {
      --header-background-opacity: 1;
    }
  }
  
  .dropdown-menu {
    gap: var(--spacing-3);
    padding: var(--spacing-5) var(--spacing-7);
    background: rgb(var(--header-background));
    color: rgb(var(--header-text));
    border-radius: var(--rounded-sm);
    opacity: 0;
    box-shadow: var(--shadow), 0 0 0 1px rgb(var(--text-color) / .12);
    z-index: 1;
    width: max-content;
    max-width: 350px;
    display: grid;
    position: absolute;
    top: calc(100% + var(--spacing-4-5));
  }
  
  .dropdown-menu:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(-1 * var(--spacing-4));
  }
  
  .dropdown-menu:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(-1 * var(--spacing-4));
  }
  
  .dropdown-menu:before {
    content: "";
    height: var(--spacing-4-5);
    width: 100%;
    position: absolute;
    bottom: 100%;
  }
  
  .dropdown-menu--restrictable {
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .dropdown-menu .dropdown-menu {
    top: calc(-1 * var(--spacing-5));
  }
  
  .dropdown-menu .dropdown-menu:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(100% + var(--spacing-7));
  }
  
  .dropdown-menu .dropdown-menu:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(100% + var(--spacing-7));
  }
  
  .dropdown-menu__item {
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-6);
    opacity: .7;
    transition: opacity .2s ease-in-out;
    display: flex;
  }
  
  [open] > .dropdown-menu__item, .dropdown-menu__item:hover {
    opacity: 1;
  }
  
  .mega-menu__promo-carousel {
    max-width: 300px;
    position: relative;
  }
  
  .mega-menu__carousel {
    grid: auto / auto-flow 45vw;
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .mega-menu__carousel {
      --content-over-media-column-gap: var(--spacing-8);
      --content-over-media-row-gap: var(--spacing-6);
      --content-over-media-content-max-width: 70%;
      grid: none;
    }
  
    .mega-menu__carousel > * {
      grid-area: 1 / -1;
    }
  
    .mega-menu__carousel-controls button {
      transition: transform .2s;
    }
  
    .mega-menu__carousel-controls button:hover {
      transform: scale(1.1);
    }
  }
  
  @media screen and (min-width: 1400px) {
    .mega-menu__promo-carousel {
      max-width: 360px;
    }
  }
  
  .mega-menu {
    --mega-menu-block-padding: var(--spacing-10);
    --mega-menu-gap: var(--spacing-12);
    --mega-menu-nav-column-gap: var(--spacing-8);
    gap: var(--mega-menu-gap);
    justify-content: var(--mega-menu-justify-content);
    padding: var(--mega-menu-block-padding) max(var(--container-gutter), 50% - var(--container-max-width) / 2);
    max-height: calc(100vh - var(--sticky-announcement-bar-enabled, 0) * var(--announcement-bar-height, 0px)  - var(--header-height, 0px)  - 20px);
    overscroll-behavior-y: contain;
    opacity: 0;
    background: rgb(var(--header-background));
    color: rgb(var(--header-text));
    mix-blend-mode: plus-lighter;
    border-block-start-width: 1px;
    align-items: flex-start;
    width: 100%;
    display: flex;
    position: absolute;
    top: 100%;
    overflow: hidden auto;
    box-shadow: 0 9999px 0 9999px #0006;
  }
  
  .mega-menu:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 0;
  }
  
  .mega-menu:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 0;
  }
  
  .mega-menu__nav {
    max-width: var(--column-list-max-width, max-content);
    gap: var(--mega-menu-nav-gap);
    flex-wrap: wrap;
    flex: auto;
    align-items: start;
    display: flex;
  }
  
  .mega-menu__nav > li {
    width: var(--mega-menu-nav-column-max-width);
  }
  
  @media screen and (min-width: 1400px) {
    .mega-menu {
      --mega-menu-gap: var(--spacing-16);
      --mega-menu-nav-column-gap: var(--spacing-12);
    }
  }
  
  @media screen and (min-width: 1600px) {
    .mega-menu {
      --mega-menu-gap: var(--spacing-20);
      --mega-menu-nav-column-gap: var(--spacing-16);
    }
  }
  
  .navigation-drawer {
    --panel-link-image-size: var(--spacing-12);
    --drawer-body-padding: var(--spacing-6);
    --navigation-drawer-width: 100vw;
    width: var(--navigation-drawer-width);
  }
  
  .navigation-drawer::part(body) {
    gap: var(--spacing-6);
    grid-template-rows: minmax(0, 1fr);
    padding: 0;
    display: grid;
    overflow-y: hidden;
  }
  
  .navigation-drawer::part(content) {
    grid-template-rows: minmax(0, 1fr);
    min-height: 70vh;
    display: grid;
  }
  
  .navigation-drawer .panel__scroller {
    align-content: flex-start;
    height: 100%;
    overflow-y: auto;
  }
  
  .navigation-drawer > [is="close-button"] {
    margin-inline-start: var(--drawer-body-padding);
  }
  
  .navigation-drawer[mobile-opening="bottom"] .panel__scroller {
    margin-block-start: calc(var(--drawer-body-padding) * -1);
    padding-block-start: var(--drawer-body-padding);
  }
  
  .navigation-drawer[mobile-opening="left"] {
    height: 100%;
  }
  
  .navigation-drawer[mobile-opening="left"]::part(body) {
    grid-template-rows: auto minmax(0, 1fr);
    padding-block-start: var(--drawer-body-padding);
  }
  
  .navigation-drawer[mobile-opening="left"] > [is="close-button"] {
    display: grid;
  }
  
  .navigation-drawer[mobile-opening="left"] .panel__wrapper {
    padding-block-start: 0;
  }
  
  .navigation-drawer [is="close-button"] {
    width: var(--spacing-10);
    height: var(--spacing-10);
    border-radius: var(--rounded-full);
    border-width: 1px;
    place-items: center;
    position: static;
  }
  
  .panel-list__wrapper {
    height: 100%;
    position: relative;
    overflow: hidden;
  }
  
  li.h3.sm\:h4 {
    font-family: 'Meltmino';
    font-weight: 500; 
  }
  
  page-dots.product-gallery__thumbnail-list.scroll-area.bleed.md\:unbleed {
    display: none;
  }
  

   .page-dots {
    display: none !important;
  }
  


  
  .panel {
    gap: var(--spacing-8);
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  
  .panel + .panel {
    opacity: 0;
    visibility: hidden;
    position: absolute;
    top: 0;
  }
  
  .panel__wrapper {
    justify-content: var(--panel-wrapper-justify-content, space-between);
    gap: var(--spacing-6);
    padding: var(--drawer-body-padding);
    flex-direction: column;
    height: 100%;
    display: flex;
    overflow-y: auto;
  }
  
  .panel-footer {
    background-color: rgb(var(--background));
  }
  
  .panel-footer__localization-wrapper {
    padding-block-start: var(--spacing-4);
  }
  
  .panel-footer__account-link:not(:only-child) {
    margin-inline-start: auto;
  }
  
  .panel-link__image {
    min-width: var(--panel-link-image-size);
    width: var(--panel-link-image-size);
    height: var(--panel-link-image-size);
    object-fit: contain;
  }
  
  @media screen and (min-width: 700px) {
    .navigation-drawer {
      --panel-link-image-size: 3.25rem;
      --drawer-body-padding: var(--spacing-8);
      --navigation-drawer-width: 412px;
    }
  
    .navigation-drawer::part(body) {
      gap: var(--spacing-8);
      grid-template-rows: auto minmax(0, 1fr);
      padding-block-start: var(--drawer-body-padding);
    }
  
    .navigation-drawer [is="close-button"] {
      width: var(--spacing-12);
      min-height: var(--spacing-12);
      height: var(--spacing-12);
    }
  
    .navigation-drawer[mobile-opening="bottom"] .panel__scroller {
      margin-block-start: 0;
      padding-block-start: 0;
    }
  
    .panel__wrapper {
      padding-block-start: 0;
    }
  }
  
  @media screen and (min-width: 1150px) {
    .navigation-drawer[mega-menu] {
      --panel-link-image-size: 3.75rem;
      --drawer-body-padding: var(--spacing-10);
      --navigation-drawer-width: 472px;
    }
  
    .navigation-drawer[mega-menu]::part(body) {
      grid-template-rows: auto;
      padding: 0;
    }
  
    .navigation-drawer[mega-menu]::part(content):before {
      content: "";
      background-color: rgb(var(--text-color) / .12);
      width: 1px;
      height: 100%;
      position: absolute;
      top: 0;
      left: 440px;
    }
  
    .navigation-drawer[mega-menu] [is="close-button"] {
      display: grid;
    }
  
    .navigation-drawer[mega-menu] > .panel-list__wrapper {
      grid-template-columns: repeat(2, minmax(440px, 1fr));
      display: grid;
    }
  
    .navigation-drawer[mega-menu] > .panel-list__wrapper > .panel {
      position: static;
    }
  
    .navigation-drawer[mega-menu] .panel + .panel > .panel__wrapper {
      padding-block-start: 6.5rem;
    }
  
    .navigation-drawer[mega-menu] .panel__wrapper {
      padding: var(--drawer-body-padding);
    }
  
    .navigation-drawer[mega-menu] .panel__scroller {
      overflow-y: initial;
    }
  
    .navigation-drawer [is="close-button"] {
      transition: transform .2s ease-in-out;
    }
  
    .navigation-drawer [is="close-button"]:hover {
      transform: rotate(90deg);
    }
  
    [dir="rtl"] .navigation-drawer[mega-menu]::part(content):before {
      left: auto;
      right: 440px;
    }
  }
  
  .mega-menu .navigation-promo__wrapper {
    flex: 1;
    max-width: max-content;
  }
  
  .navigation-promo {
    grid: var(--navigation-promo-grid);
    align-content: start;
    align-items: start;
    gap: var(--navigation-promo-gap);
    display: grid;
  }
  
  .navigation-promo.navigation-promo--carousel .content-over-media p {
    max-width: 75%;
  }
  
  .navigation-promo .product-card {
    padding: var(--spacing-8);
    flex-direction: column;
    justify-content: center;
    height: 100%;
    display: flex;
  }
  
  .navigation-promo .product-card__figure {
    max-width: 160px;
    margin-block-end: var(--spacing-4);
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
  
  .navigation-promo .product-card__quick-buy {
    display: none;
  }
  
  .navigation-promo .product-card__info {
    padding: 0;
  }
  
  .navigation-promo .product-card__image {
    border-radius: 0;
  }
  
  .navigation-promo__carousel-controls {
    gap: var(--spacing-2);
    color: rgb(var(--text-color));
    z-index: 1;
    transition: color .2s ease-in-out;
    display: flex;
    position: absolute;
  }
  
  .navigation-promo__carousel-controls:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--content-over-media-column-gap);
  }
  
  .navigation-promo__carousel-controls:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--content-over-media-column-gap);
  }
  
  @media screen and (min-width: 1150px) {
    .navigation-promo__carousel-controls > button svg {
      transition: scale .2s ease-in-out;
    }
  
    .navigation-promo__carousel-controls > button:hover svg {
      scale: 1.1;
    }
  }
  
  .shopify-section--hot-spots {
    z-index: 1;
  }
  
  .hot-spot {
    --hot-spot-dot-size: var(--spacing-8);
    --hot-spot-dot-shadow-size: var(--spacing-2-5);
    top: var(--hot-spot-vertical-position);
    left: var(--hot-spot-horizontal-position);
    grid-area: unset !important;
    z-index: unset !important;
    position: absolute !important;
  }
  
  .hot-spot__dot {
    width: var(--hot-spot-dot-size);
    height: var(--hot-spot-dot-size);
    border-radius: var(--rounded-full);
    background: rgb(var(--hot-spot-background));
    color: rgb(var(--hot-spot-text-color));
    place-items: center;
    transition: transform .2s ease-in-out;
    display: grid;
    position: relative;
  }
  
  .hot-spot__dot:after {
    content: "";
    box-sizing: content-box;
    top: calc(-1 * var(--hot-spot-dot-shadow-size));
    left: calc(-1 * var(--hot-spot-dot-shadow-size));
    padding: var(--hot-spot-dot-shadow-size);
    background: radial-gradient(50% 50% at 50% 50%, rgb(var(--hot-spot-background) / 0) 0%, rgb(var(--hot-spot-background) / .3) 100%);
    border-radius: inherit;
    width: 100%;
    height: 100%;
    animation: 2s ease-in-out infinite alternate ping;
    position: absolute;
  }
  
  .hot-spot__dot[aria-expanded="true"] {
    transform: rotate(45deg);
  }
  
  .hot-spot-popover {
    --popover-body-padding: var(--spacing-6);
  }
  
  .hot-spot-popover::part(title) {
    display: none;
  }
  
  .hot-spot-popover::part(body) {
    text-align: start;
  }
  
  @media screen and (min-width: 700px) {
    .hot-spot {
      --hot-spot-dot-size: var(--spacing-12);
      --hot-spot-dot-shadow-size: var(--spacing-4);
    }
  
    .hot-spot__dot svg {
      width: 12px;
      height: 12px;
    }
  
    .hot-spot-popover {
      --popover-body-padding: var(--spacing-8);
    }
  }
  
  @media screen and (min-width: 1000px) {
    .hot-spot-popover {
      --popover-anchor-inline-spacing: calc(100% + var(--spacing-4-5));
    }
  
    .hot-spot-popover::part(content) {
      background: rgb(var(--hot-spot-content-background) / var(--hot-spot-content-opacity));
      color: rgb(var(--hot-spot-content-text-color));
      -webkit-backdrop-filter: blur(var(--hot-spot-content-blur-radius));
      backdrop-filter: blur(var(--hot-spot-content-blur-radius));
    }
  }
  
  .image-link-blocks {
    grid: var(--image-link-blocks-grid);
    gap: var(--spacing-2);
    align-items: start;
    display: grid;
  }
  
  .image-link-blocks__item .content-over-media .circle-chevron {
    background: rgb(var(--text-color));
    color: rgb(var(--background));
    opacity: 0;
    transition: opacity .2s ease-in-out;
  }
  
  .image-link-blocks__item .icon-circle-button-right-clipped {
    opacity: 0;
    transition: opacity .2s ease-in-out;
  }
  
  .image-link-blocks__inner {
    gap: var(--spacing-3);
    padding: var(--spacing-5);
    grid-template-columns: minmax(0, 1fr);
    display: grid;
  }
  
  .image-link-blocks__inner .circle-chevron {
    opacity: 0;
    transition: opacity .2s ease-in-out;
  }
  
  .image-link-blocks__title {
    gap: var(--spacing-2-5);
    justify-content: center;
    align-items: center;
    margin-block-end: calc(-1 * var(--spacing-1));
    display: flex;
  }
  
  .image-link-blocks__title--animate {
    transform: translateX(calc(var(--transform-logical-flip) * 16px));
    transition: transform .2s;
  }
  
  @media screen and (min-width: 700px) {
    .image-link-blocks {
      gap: var(--grid-gutter);
    }
  
    .image-link-blocks__inner {
      gap: var(--spacing-5);
      padding: var(--spacing-10);
    }
  
    .image-link-blocks__title {
      margin-block-end: calc(-1 * var(--spacing-4));
    }
  }
  
  @media screen and (min-width: 1000px) {
    .image-link-blocks__item {
      scroll-snap-align: start;
      scroll-snap-stop: always;
    }
  }
  
  @media screen and (pointer: fine) {
    .image-link-blocks__item:hover .content-over-media .circle-chevron {
      background: rgb(var(--text-color));
      color: rgb(var(--background));
      opacity: 1;
    }
  
    .image-link-blocks__item:hover .image-link-blocks__inner .circle-chevron, .image-link-blocks__item:hover .icon-circle-button-right-clipped {
      opacity: 1;
    }
  
    .image-link-blocks__item:hover .image-link-blocks__title--animate {
      transform: translateX(0);
    }
  }
  
  .shopify-section--collection-banner + .shopify-section--image-link-blocks {
    --section-outer-spacing-block: var(--grid-gutter);
  }
  
  .images-scrolling__content {
    gap: var(--spacing-4);
    display: grid;
  }
  
  .images-scrolling__counter {
    align-items: center;
    gap: var(--spacing-4);
    display: flex;
  }
  
  .images-scrolling__counter:before {
    content: "";
    background: currentColor;
    width: 1.5rem;
    height: .125rem;
  }
  
  .images-scrolling__icon {
    max-width: calc(var(--images-scrolling-item-icon-width) / 1.5);
  }
  
  @media screen and (min-width: 700px) {
    .images-scrolling__content {
      gap: var(--spacing-8);
      padding-block: var(--spacing-8);
      order: -1;
      align-content: start;
    }
  
    .images-scrolling__counter {
      gap: var(--spacing-6);
    }
  
    .images-scrolling__counter:before {
      width: 2.5rem;
    }
  
    .images-scrolling__icon {
      max-width: calc(var(--images-scrolling-item-icon-width));
    }
  }
  
  .images-scrolling-mobile {
    grid: var(--images-scrolling-grid);
    gap: var(--spacing-12) var(--container-gutter);
    display: grid;
  }
  
  .images-scrolling-mobile__item {
    align-content: start;
    gap: var(--spacing-4);
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .images-scrolling-mobile {
      display: none;
    }
  }
  
  .images-scrolling-desktop {
    grid-template-columns: var(--images-scrolling-grid-template-columns);
    grid-template-rows: repeat(var(--images-scrolling-block-count), minmax(0, 1fr));
    column-gap: var(--spacing-8);
    display: grid;
  }
  
  .images-scrolling-desktop__media-wrapper {
    top: calc(var(--sticky-area-height)  + 20px);
    grid-column: media;
    position: sticky;
  }
  
  .images-scrolling-desktop__media-wrapper > * {
    object-fit: cover;
    object-position: center;
    will-change: transform;
  }
  
  .images-scrolling-desktop__media-wrapper > :first-child {
    max-height: calc(100vh - var(--sticky-area-height)  - 40px);
  }
  
  @supports (max-height: 100svh) {
    .images-scrolling-desktop__media-wrapper > :first-child {
      max-height: calc(100svh - var(--sticky-area-height)  - 40px);
    }
  }
  
  .images-scrolling-desktop__media-wrapper > :not(:first-child) {
    opacity: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  
  .images-scrolling-desktop__content-list {
    grid-area: 1 / content / -1;
    grid-auto-columns: minmax(0, 1fr);
    align-items: center;
    display: grid;
  }
  
  @media screen and (min-width: 1000px) {
    .images-scrolling-desktop {
      column-gap: var(--spacing-16);
    }
  }
  
  @media screen and (min-width: 1150px) {
    .images-scrolling-desktop {
      column-gap: var(--spacing-28);
    }
  }
  
  @media screen and (max-width: 699px) {
    .images-scrolling-desktop {
      display: none;
    }
  }
  
  .impact-text {
    --impact-text-spacing: var(--spacing-10);
    gap: var(--impact-text-spacing);
    align-items: start;
    display: grid;
  }
  
  .impact-text--scroll {
    grid: auto / auto-flow var(--impact-text-auto-columns);
  }
  
  .impact-text--center {
    text-align: center;
    justify-content: safe center;
  }
  
  .impact-text--end {
    text-align: end;
    justify-content: end;
  }
  
  .impact-text__text {
    font-size: var(--impact-text-font-size);
    letter-spacing: 0;
    line-height: 1;
  }
  
  .impact-text__text:not(:only-child) {
    margin-bottom: .2em;
  }
  
  .impact-text__content {
    max-width: 750px;
  }
  
  .impact-text--center .impact-text__content {
    /* margin-inline-start: auto; */
    margin-inline-end: auto;
  }
  
  .impact-text--end .impact-text__content {
    margin-inline-start: auto;
  }
  
  @media screen and (min-width: 700px) {
    .impact-text {
      --impact-text-spacing: var(--spacing-8);
      grid: auto / auto-flow var(--impact-text-auto-columns);
    }
  
    .impact-text--center {
      justify-content: space-evenly;
    }
  }
  
  @media screen and (min-width: 1150px) {
    .impact-text {
      --impact-text-spacing: var(--spacing-12);
    }
  }
  
  
  .whats-inside {
    --whats-inside-spacing: var(--spacing-10);
    gap: var(--whats-inside-spacing);
    align-items: start;
    display: grid;
  }
  
  .whats-inside--scroll {
    grid: auto / auto-flow var(--whats-inside-auto-columns);
  }
  
  .whats-inside--center {
    text-align: center;
    justify-content: safe center;
  }
  
  .whats-inside--end {
    text-align: end;
    justify-content: end;
  }
  
  .whats-inside__text {
    font-size: var(--whats-inside-font-size);
    letter-spacing: 0;
    line-height: 1;
  }
  
  .whats-inside__text:not(:only-child) {
    margin-bottom: .2em;
  }
  
  .whats-inside__content {
    max-width: 750px;
  }
  
  .whats-inside--center .whats-inside__content {
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
  
  .whats-inside--end .whats-inside__content {
    margin-inline-start: auto;
  }
  
  @media screen and (min-width: 700px) {
    .whats-inside {
      --whats-inside-spacing: var(--spacing-8);
      grid: auto / auto-flow var(--whats-inside-auto-columns);
    }
  
    .whats-inside--center {
      justify-content: space-evenly;
    }
  }
  
  @media screen and (min-width: 1150px) {
    .whats-inside {
      --whats-inside-spacing: var(--spacing-12);
    }
  }
  
  .logo-list {
    grid: var(--logo-list-grid);
    gap: var(--spacing-2);
    display: grid;
  }
  
  .logo-list__item {
    padding: var(--spacing-6);
    border: 1px solid rgb(var(--logo-list-items-border));
    justify-content: center;
    align-items: center;
    display: flex;
  }
  
  .logo-list__image {
    max-width: var(--logo-list-image-max-width, none);
    width: 100%;
  }
  
  @media screen and (min-width: 700px) {
    .logo-list {
      gap: var(--spacing-6);
    }
  
    .logo-list__item {
      padding: var(--spacing-11);
    }
  }
  
  .media-grid {
    align-items: start;
    gap: calc(var(--grid-gutter) / 2);
    grid: auto-flow dense var(--media-grid-row-height) / repeat(2, minmax(0, 1fr));
    display: grid;
  }
  
  .media-grid__item {
    grid-area: span min(2, var(--media-grid-row-span)) / span min(2, var(--media-grid-column-span));
    height: 100%;
  }
  
  .media-grid__item > * {
    height: 100%;
  }
  
  @media screen and (min-width: 700px) {
    .media-grid {
      --calculated-row-height: max(150px, min(100vw / 5, var(--media-grid-row-height)));
      grid: auto-flow dense var(--calculated-row-height) / repeat(4, minmax(0, 1fr));
    }
  
    .media-grid__item {
      grid-area: span var(--media-grid-row-span) / span var(--media-grid-column-span);
    }
  }
  
  @media screen and (min-width: 1000px) {
    .media-grid {
      gap: var(--grid-gutter);
    }
  }
  
  .media-with-text {
    --media-with-text-content-padding: var(--spacing-10) var(--spacing-8);
    gap: var(--media-with-text-gap);
    display: grid;
    overflow: hidden;
  }
  
  .media-with-text__item {
    gap: min(var(--media-with-text-gap), var(--grid-gutter));
    grid-template: var(--media-with-text-item-grid-template);
    display: grid;
  }
  
  .media-with-text__media {
    grid-area: media;
    position: relative;
    overflow: hidden;
  }
  
  .media-with-text__media > * {
    object-fit: cover;
    object-position: center;
    border-radius: inherit;
    width: 100%;
    height: 100%;
  }
  
  .media-with-text__content {
    padding: var(--media-with-text-content-padding);
    grid-area: content;
    display: grid;
  }
  
  .media-with-text__icon {
    max-width: calc(var(--media-with-text-item-icon-width) / 1.5);
    margin-block-end: var(--spacing-1);
    display: inline-block;
  }
  
  @media screen and (min-width: 700px) {
    .media-with-text {
      --media-with-text-content-padding: var(--spacing-16);
    }
  
    .media-with-text__icon {
      max-width: var(--media-with-text-item-icon-width);
      margin-block-end: var(--spacing-2);
    }
  }
  
  @media screen and (min-width: 1000px) {
    .media-with-text {
      --media-with-text-content-padding: var(--spacing-12);
    }
  
    .media-with-text__item {
      gap: var(--media-with-text-gap);
    }
  }
  
  @media screen and (min-width: 1150px) {
    .media-with-text {
      --media-with-text-content-padding: var(--spacing-18);
    }
  }
  
  @media screen and (min-width: 1400px) {
    .media-with-text {
      --media-with-text-content-padding: var(--spacing-20);
    }
  }
  
  .multi-column {
    --multi-column-list-column-max-gap: var(--grid-gutter);
    --multi-column-list-row-min-gap: var(--grid-gutter);
    --multi-column-item-gap: var(--spacing-6);
    grid: var(--multi-column-grid);
    gap: max(var(--multi-column-list-gap), var(--multi-column-list-row-min-gap)) min(var(--multi-column-list-column-max-gap), var(--multi-column-list-gap));
    display: grid;
  }
  
  .multi-column__item {
    align-content: start;
    gap: var(--multi-column-item-gap);
    grid-template-columns: minmax(0, 1fr);
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .multi-column {
      --multi-column-list-column-max-gap: var(--spacing-16);
      grid: auto / auto-flow 38vw;
    }
  }
  
  @media screen and (min-width: 1000px) {
    .multi-column {
      --multi-column-list-row-min-gap: var(--spacing-16);
      grid: auto / repeat(12, minmax(0, 1fr));
    }
  
    .multi-column__item {
      grid-column: var(--multi-column-item-column-count);
    }
  }
  
  @media screen and (min-width: 1400px) {
    .multi-column {
      --multi-column-list-column-max-gap: var(--multi-column-list-gap);
    }
  }
  
  .multiple-images-with-text {
    grid-template-columns: var(--multiple-images-with-text-grid-template-columns, none);
    gap: var(--section-stack-spacing-block) var(--multiple-images-with-text-column-gap, 0px);
    max-width: var(--multiple-images-with-text-max-width);
    isolation: isolate;
    justify-content: center;
    place-items: center;
    margin-inline-start: auto;
    margin-inline-end: auto;
    display: grid;
  }
  
  .multiple-images-with-text__content-with-nav {
    row-gap: var(--spacing-8);
    text-align: var(--multiple-images-with-text-alignment, center);
    justify-items: var(--multiple-images-with-text-alignment, center);
    justify-self: start;
    width: 100%;
    max-width: 600px;
    display: grid;
  }
  
  .multiple-images-with-text__image-list[layout="stacked"], .multiple-images-with-text__content-list {
    place-items: center;
    width: 100%;
    display: grid;
  }
  
  :is(.multiple-images-with-text__image-list[layout="stacked"], .multiple-images-with-text__content-list) > * {
    grid-area: 1 / -1;
    width: 100%;
  }
  
  .multiple-images-with-text__image-list {
    grid-template: var(--multiple-images-with-text-images-grid-template, none);
    align-items: var(--multiple-images-with-text-images-alignment, center);
    width: 100%;
    display: grid;
  }
  
  .multiple-images-with-text__image-list > img {
    max-width: var(--multiple-images-with-text-image-max-width, 390px);
    will-change: transform;
    outline: 1px solid #0000;
    width: 100%;
    margin-inline-start: auto;
    margin-inline-end: auto;
  }
  
  .multiple-images-with-text__image-list:not([layout="stacked"]) > img:first-child {
    grid-area: 1 / 2 / -1;
    margin-block-start: var(--multiple-images-with-text-main-image-offset, 0px);
  }
  
  .multiple-images-with-text__content-list {
    place-items: end start;
  }
  
  .multiple-images-with-text__image-list[layout="stacked"] {
    --multiple-images-with-text-image-max-width: 520px;
  }
  
  .multiple-images-with-text__image-list[layout="collage"] {
    gap: var(--grid-gutter);
  }
  
  @media screen and (max-width: 699px) {
    .multiple-images-with-text__content-with-nav .circle-button {
      width: 2.5rem;
      height: 2.5rem;
    }
  }
  
  @media screen and (min-width: 700px) {
    .multiple-images-with-text__content-with-nav {
      row-gap: var(--spacing-12);
    }
  }
  
  @media screen and (min-width: 1150px) {
    .multiple-images-with-text {
      --multiple-images-with-text-image-max-width: 500px;
    }
  }
  
  .newsletter-content {
    gap: var(--spacing-6);
    max-width: 780px;
    margin-inline-start: auto;
    margin-inline-end: auto;
    display: grid;
  }
  
  .newsletter-box {
    padding: var(--spacing-6);
  }
  
  .newsletter-content > .form {
    width: 100%;
  }
  
  @media screen and (min-width: 700px) {
    .newsletter {
      border-radius: inherit;
      overflow: hidden;
    }
  
    .newsletter-content {
      gap: var(--spacing-8);
    }
  
    .newsletter-content__icon {
      width: 48px;
      height: 48px;
    }
  
    .newsletter-box {
      padding: var(--spacing-12);
    }
  }
  
  @media screen and (min-width: 1000px) {
    .newsletter {
      grid-template-columns: .5fr .5fr;
      display: grid;
    }
  
    .newsletter-content > .form {
      max-width: 490px;
    }
  
    .newsletter > .newsletter-box {
      justify-content: start;
      align-items: center;
      display: grid;
    }
  
    .section-full .newsletter-box {
      background: none;
    }
  }
  
  @media screen and (min-width: 1150px) {
    .newsletter-box {
      padding: var(--spacing-16);
    }
  }
  
  @media screen and (min-width: 1400px) {
    .newsletter-box {
      padding: var(--spacing-20);
    }
  }
  
  .newsletter-drawer {
    --drawer-content-max-height: 80vh;
    --drawer-body-padding: 0;
    height: auto;
  }
  
  .newsletter-drawer__content {
    padding: var(--spacing-6) var(--spacing-8) var(--spacing-8);
  }
  
  @media screen and (min-width: 700px) {
    .newsletter-drawer {
      --drawer-content-max-height: calc(100vh - var(--spacing-8));
      width: calc(445px + var(--spacing-8));
    }
  
    .newsletter-drawer button[is="close-button"] {
      top: var(--spacing-8);
    }
  
    .newsletter-drawer button[is="close-button"]:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      right: var(--spacing-8);
    }
  
    .newsletter-drawer button[is="close-button"]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      left: var(--spacing-8);
    }
  
    .newsletter-drawer__content {
      padding: var(--spacing-10) var(--spacing-12) var(--spacing-12);
    }
  }
  
  .not-found {
    --not-found-font-size: 210px;
    padding-block-start: var(--spacing-48);
    padding-block-end: var(--spacing-48);
    position: relative;
    overflow: hidden;
  }
  
  .not-found:before {
    content: "404";
    letter-spacing: 0;
    opacity: .1;
    pointer-events: none;
    font-weight: bold;
    font-size: var(--not-found-font-size);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  @media screen and (min-width: 700px) {
    .not-found {
      --not-found-font-size: 400px;
      padding-block-start: var(--spacing-80);
      padding-block-end: var(--spacing-80);
    }
  }
  
  .page {
    gap: var(--spacing-12);
    max-width: var(--page-max-width, 80ch);
    grid-auto-columns: minmax(0, 1fr);
    margin-inline-start: auto;
    margin-inline-end: auto;
    display: grid;
  }
  
  .password {
    min-height: 100vh;
    grid-template-rows: 1fr;
    align-items: center;
    min-height: 100dvh;
    padding-block-start: var(--spacing-10);
    padding-block-end: var(--spacing-10);
    display: grid;
    position: relative;
  }
  
  .password:after {
    content: "";
    width: calc(100vw - var(--scrollbar-width, 0px));
    pointer-events: none;
    background-image: linear-gradient(#0000 0%, #0003 100%);
    height: 60px;
    margin-inline-start: calc(50% - 50vw);
    position: absolute;
    bottom: 0;
  }
  
  .password:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
    left: 0;
  }
  
  .password:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):after {
    right: 0;
  }
  
  .password__main {
    gap: var(--spacing-10);
    width: 100%;
    max-width: 500px;
    display: grid;
  }
  
  .password__social-box {
    gap: var(--spacing-4);
    padding: var(--spacing-5) var(--spacing-8);
    justify-content: center;
    display: grid;
  }
  
  .password__aside {
    align-self: end;
    justify-items: center;
    gap: var(--spacing-2);
    margin-top: var(--spacing-8);
    display: grid;
    position: relative;
  }
  
  .password__storefront-drawer {
    --drawer-body-padding: var(--spacing-8);
  }
  
  .password__storefront-form {
    gap: var(--spacing-6);
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .password--center {
      justify-items: center;
    }
  
    .password--end {
      justify-items: end;
    }
  
    .password__main {
      gap: var(--spacing-12);
    }
  
    .password__social-box {
      gap: var(--spacing-6);
      padding: var(--spacing-8) var(--spacing-12);
      align-items: center;
      display: flex;
    }
  
    .password__aside {
      justify-self: end;
      display: flex;
    }
  
    .password--center .password__aside {
      justify-self: center;
    }
  
    .password--end .password__aside {
      justify-self: start;
    }
  
    .password__storefront-drawer {
      --drawer-body-padding: var(--spacing-10);
    }
  }
  
  .press {
    --press-padding-inline: var(--spacing-5);
    gap: var(--spacing-6);
    max-width: var(--press-max-width);
    margin-inline-start: auto;
    margin-inline-end: auto;
    padding-inline-start: var(--press-padding-inline);
    padding-inline-end: var(--press-padding-inline);
    display: grid;
  }
  
  .press__list {
    align-items: flex-start;
    display: grid;
  }
  
  .press__list > * {
    grid-area: 1 / -1;
  }
  
  .press__list-item {
    gap: var(--spacing-8);
    justify-items: center;
    display: grid;
  }
  
  .press__list-item:not(.is-selected) {
    opacity: 0;
    visibility: hidden;
  }
  
  .press__list-item .rating {
    margin-block-end: calc(var(--press-padding-inline) * -1);
  }
  
  .press__list-item .blockquote:first-child {
    margin-block-start: 0;
  }
  
  .press__logo {
    border: 1px solid rgb(var(--press-items-border));
    justify-content: center;
    align-items: center;
    display: flex;
  }
  
  .press__logo.bg-custom {
    padding: var(--spacing-6);
  }
  
  .press__image {
    max-width: var(--press-image-max-width, none);
    width: 100%;
  }
  
  .press__controls {
    gap: var(--spacing-5);
    grid: auto / auto-flow;
    justify-content: center;
    align-items: center;
    display: grid;
  }
  
  @media screen and (min-width: 700px) {
    .press {
      --press-padding-inline: var(--spacing-8);
      gap: var(--spacing-10);
    }
  
    .press__list-item {
      gap: var(--spacing-10);
    }
  
    .press .rating__stars svg {
      width: 20px;
      height: 20px;
    }
  }
  
  .product {
    grid: var(--product-grid);
    gap: var(--container-gutter) var(--spacing-10);
    align-items: start;
    display: grid;
  }
  
  @media screen and (min-width: 1150px) {
    .product {
      column-gap: var(--spacing-12);
    }
  }
  
  @media screen and (min-width: 1600px) {
    .product {
      column-gap: var(--spacing-24);
    }
  }
  
  .product-info {
    --product-info-block-spacing: var(--spacing-6);
  }
  
  .product-info > * + * {
    margin-block-start: var(--product-info-block-spacing);
    margin-block-end: var(--product-info-block-spacing);
  }
  
  .product-info > * + *:last-child {
    margin-block-end: 0;
  }
  
  .product-info__accordion {
    --product-info-block-spacing: 0;
  }
  
  .product-info__block-item:where([data-block-type="vendor"], [data-block-type="title"], [data-block-type="sku"], [data-block-type="price"], [data-block-type="rating"], [data-block-type="payment-terms"]) {
    --product-info-block-spacing: var(--spacing-4);
  }
  
  .product-info__block-item:where([data-block-type="badges"]) {
    --product-info-block-spacing: var(--spacing-4);
  }
  
  .product-info__sku {
    display: block;
  }
  
  .product-info__badge-list {
    gap: var(--spacing-2);
    flex-wrap: wrap;
    display: flex;
  }
  
  .product-info__price .rating-with-text {
    justify-content: flex-start;
    display: flex;
  }
  
  .product-info__price .product-info__badge-list {
    align-self: center;
    margin-inline-start: var(--spacing-1);
  }
  
  .product-info__price .rating {
    margin-inline-start: auto;
  }
  
  .product-info__offer-list {
    gap: var(--spacing-2);
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    display: grid;
  }
  
  .product-info__share-buttons {
    display: none;
  }
  
  .native-share--disabled .product-info__share-buttons {
    display: block;
  }
  
  .product-info__native-share {
    display: none;
  }
  
  .native-share--enabled .product-info__native-share {
    align-items: center;
    gap: var(--spacing-2-5);
    opacity: .7;
    transition: opacity .2s ease-in-out;
    display: flex;
  }
  
  .native-share--enabled .product-info__native-share:hover {
    opacity: 1;
  }
  
  .product-info__block-item:empty {
    display: none;
  }
  
  .product-info__block-item:has(.accordion) + .product-info__block-item:has(.accordion) .accordion {
    border-block-start-width: 0;
  }
  
  @media screen and (min-width: 700px) {
    .product-info__block-item:where([data-block-type="vendor"], [data-block-type="title"], [data-block-type="sku"], [data-block-type="price"], [data-block-type="rating"], [data-block-type="payment-terms"]) {
      --product-info-block-spacing: var(--spacing-4);
    }
  
    .product-info__block-item:where([data-block-type="buy-buttons"]) {
      --product-info-block-spacing: var(--spacing-8);
    }
  
    .product-info__offer-list {
      gap: var(--spacing-4);
    }
  
    .product-info__complementary-products .horizontal-product-list-carousel:not(.separate) {
      border-width: 1px;
    }
  
    .product-info__complementary-products .horizontal-product-list-carousel > .horizontal-product-list, .product-info__complementary-products .horizontal-product-list-carousel .horizontal-product {
      border: none;
    }
  }
  
  @media screen and (min-width: 1000px) {
    .product-info {
      top: calc(var(--sticky-area-height)  + 20px);
      z-index: 1;
      position: sticky;
    }
  }
  
  .product-gallery {
    gap: var(--spacing-5);
    grid-auto-columns: minmax(0, 1fr);
    display: grid;
  }
  
  .product-gallery__ar-wrapper {
    gap: var(--spacing-2-5);
    display: grid;
  }
  
  .product-gallery__media-list-wrapper {
    display: grid;
    position: relative;
  }
  
  /* Prevent clicks on the media items from changing slides */
  .product-gallery__media {
    pointer-events: none;
  }
  
  /* Ensure navigation arrows still receive click events */
  .product-gallery__nav-arrow {
    pointer-events: auto;
  }
  
  /* Allow clicks on videos and other interactive media elements */
  .product-gallery__media [data-media-type="video"],
  .product-gallery__media [data-media-type="external_video"],
  .product-gallery__media model-viewer,
  .product-gallery__media .shopify-model-viewer-ui,
  .product-gallery__media video,
  .product-gallery__media iframe {
    pointer-events: auto;
  }
  
  /* Allow zoom functionality to still work */
  .product-gallery__zoom {
    pointer-events: auto;
  }

  /* Reduce whitespace between header and product images on mobile */
  @media screen and (max-width: 999px) {
    .product {
      margin-top: -15px;
    }
    
    #shopify-section-main-product {
      margin-top: 0 !important;
    }
    
    .product-media-section {
      margin-top: -10px;
    }
  }
.images-scrolling__content p.h1 {
    font-weight: 800;
    font-size: 90px;
    line-height: 100%;
  }
  .images-scrolling__content p.subheading-text {
    margin: 10px 0 !important;
    font-weight: 600;
 }
  .images-scrolling__content a.button.button--xl {
    margin-top: 15px !important;
    font-weight: 700;
    font-size: 22px;
    padding: 7px 16px;
 }
  .images-scrolling-desktop__content-list .images-scrolling__content {
    padding-left: 0 !important;
    padding-right: 0 !important;
 } 
.footer-logo {
    max-width: 68%;
}
html body .jdgm-rev-widg__header {
    text-align: left;
    display: flex;
      align-items: center;
      gap: 50px;
}
html body h2.jdgm-rev-widg__title {
    font-size: 62px !important;
    text-transform: uppercase !important;
      width: 65%;
}
html body .jdgm-row-stars {
    width: 35%;
    flex-direction: column;
}
html body .jdgm-widget-actions-wrapper {
    text-align: center;
    margin: 0 !important;
}
html body .jdgm-rev-widg__summary {
    width: 100% !important;
    justify-content: left !important;
    text-align: left;
    margin-left: 0;
}
html body .jdgm-rev-widg__summary-inner {
    display: flex;
    width: 100%;
}
html body span.jdgm-rev-widg__summary-average {
    font-weight: 500;
    font-size: 20px !important;
    color: #2A2A2A;
}
html body .jdgm-widget-actions-wrapper {
    width: fit-content !important;
}
@media screen and (max-width: 480px) {
html body .product-info h1.product-info__title.h3 {
    font-size: 40px !important;
}
}
@media screen and (max-width: 999px) {
    .product-badges.grid-layout {
        grid-template-columns: repeat(4, 1fr) !important;
              display: grid !important;
    }
.product-badges.product-badges--mobile.grid-layout {
    display: none !important;
}
.product-info.variant-selected-flavor-variety-pack {
    border: 3px solid !important;
    border-radius: 1rem;
    padding: 15px;
}
.flavor-info-items {
        flex-wrap: wrap;
    }
html body .jdgm-rev-widg__header {
    flex-direction: column;
    justify-content: left;
    gap: 10px;
}
html body h2.jdgm-rev-widg__title {
    font-size: 40px !important;
    text-transform: uppercase !important;
    width: 100%;
    margin-bottom: 8px !important;
  font-family: Meltmino !important;
}
html body .jdgm-row-stars {
    width: 100%;
    flex-direction: column;
}
html body .jdgm-review-widget--medium .jdgm-write-rev-link {
    max-width: 100%;
}










  
}



