/*! PhotoSwipe main CSS by <PERSON><PERSON><PERSON> | photoswipe.com */
*, :before, :after {
  box-sizing: border-box;
  border-style: solid;
  border-width: 0;
  border-color: rgb(var(--text-color) / .12);
}

html {
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  interpolate-size: allow-keywords;
  scroll-padding-block-start: calc(var(--sticky-area-height)  + 20px);
  line-height: 1.5;
}

body {
  --background: var(--background-primary);
  --text-color: var(--text-primary);
  background: rgb(var(--background));
  color: rgb(var(--text-color));
  font: var(--text-font-style) var(--text-font-weight) var(--text-base) / 1.6 var(--text-font-family);
  letter-spacing: var(--text-letter-spacing);
  margin: 0;
  position: relative;
  overflow-x: hidden;
}


hr {
  color: inherit;
  border-top-width: 1px;
  height: 0;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  font-size: 1em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  font-style: inherit;
  line-height: inherit;
  text-transform: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button, [type="button"], [type="reset"], [type="submit"] {
  -webkit-appearance: button;
  text-align: inherit;
  background-color: #0000;
  background-image: none;
}

button, label, summary, [role="button"], [type="checkbox"], [type="radio"], [type="submit"] {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  cursor: pointer;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

::-webkit-inner-spin-button {
  height: auto;
}

::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

::-webkit-date-and-time-value {
  text-align: start;
}

summary {
  -webkit-user-select: none;
  user-select: none;
  list-style-type: none;
}

summary::-webkit-details-marker {
  display: none;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  margin: 0;
  padding: 0;
  list-style: none;
}

textarea {
  resize: vertical;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

:disabled {
  cursor: default;
}

img, video, iframe, object {
  vertical-align: middle;
  display: block;
}

img {
  color: #0000;
  max-width: min(var(--image-mobile-max-width, var(--image-max-width, 100%)), 100%);
  object-fit: inherit;
  object-position: inherit;
  height: auto;
}

picture source {
  display: contents;
}

picture img {
  min-height: inherit;
  width: 100%;
  max-height: 100%;
}

video {
  width: 100%;
  max-width: 100%;
  height: auto;
}

:focus:not(:focus-visible) {
  outline: none;
}

[hidden] {
  display: none !important;
}

height-observer, visibility-progress, scroll-area, video-media, model-media, media-carousel, impact-text, whats-inside, recently-viewed-products {
  display: block;
}

@media screen and (min-width: 700px) {
  img {
    max-width: min(var(--image-max-width, 100%), 100%);
  }
}
/* 
.images-scrolling-mobile__item.snap-start {
  z-index: 999;
} */

.em, em {
  background-color: #2A2A2A !important;
  color: #E7E8E5 !important;
  padding: 0.20em .1em;
  display: inline;
  font-style: normal !important; 
}

.jdgm-rev-widg__title {
  margin-bottom: 1rem !important; 
}

.jdgm-rev__timestamp, .jdgm-rev__pinned {
  float: none !important; 
  color: #2A2A2A !important;
  font-family: var(--heading-font-family) !important;
  letter-spacing: 1px; 
}

.jdgm-histogram.jdgm-temp-hidden {
  display: none !important;
}

.jdgm-rev-widg__summary {
  margin-bottom: 1rem !important; 
}

.jdgm-rev-widg__title {
  visibility: unset;
  font-family: var(--heading-font-family) !important;
  font-weight: var(--heading-font-weight) !important;
  font-size: var(--text-h4) !important; 
}

.jdgm-rev__title {
  visibility: unset;
  font-family: var(--heading-font-family) !important;
  font-weight: var(--heading-font-weight) !important;
  font-size: var(--text-h6) !important; 
}

a.jdgm-paginate__page {
  color: #2A2A2A !important;
  font-size: medium !important;
  opacity: 1 !important; 
}
.jdgm-paginate__page.jdgm-curt {
  font-size: 100% !important; 
  opacity: 1 !important; 
}

.jdgm-rev__body {
  font-size: medium !important; 
  opacity: 1 !important; 
}

span.jdgm-rev-widg__summary-average {
  font-size: medium !important; 
}

a.jdgm-write-rev-link {
  text-transform:uppercase !important; 
  font-weight: 400 !important; 
  color: #E7E8E5 !important;
  background-color: #2A2A2A !important;
  border-radius: var(--rounded-button);
}

.jdgm-histogram.jdgm-temp-hidden {
  display: none;
}

.jdgm-rev-widg__summary-text {
  padding: 5px;
  font-size: medium;
}

.jdgm-rev__author::before {
  content: "verified";
  background-color: #2A2A2A !important;
  color: #E7E8E5 !important;
  padding: 0.1em 0.3em;
  margin-right: 0.5em;
  display: inline-block;
  font-size: 0.8em !important;
  text-transform: capitalize;
  font-family: var(--heading-font-family) !important;
  letter-spacing: 1px; 
  vertical-align: middle; 
}

.jdgm-rev__icon {
  display: none !important;
}

.jdgm-rev__author {
  font-family: var(--heading-font-family) !important;
  vertical-align: middle;
  font-size: 0.8em !important;
}

.jdgm-row-extra {
  display: none !important;
}

.pswp {
  --pswp-bg: rgb(var(--background-primary));
  --pswp-placeholder-bg: rgb(var(--background-primary));
  --pswp-root-z-index: 100000;
  --pswp-icon-color: rgb(var(--background-primary));
  --pswp-icon-color-secondary: rgb(var(--background-primary));
  --pswp-icon-stroke-color: rgb(var(--text-primary));
  --pswp-icon-stroke-width: 2px;
  --pswp-error-text-color: rgb(var(--text-primary));
  z-index: var(--pswp-root-z-index);
  touch-action: none;
  opacity: .003;
  contain: layout style size;
  -webkit-tap-highlight-color: #0000;
  outline: 0;
  width: 100%;
  height: 100%;
  display: none;
  position: fixed;
  top: 0;
  left: 0;
}

.pswp img {
  max-width: none;
}

.pswp--open {
  display: block;
}

.pswp, .pswp__bg {
  will-change: opacity;
  transform: translateZ(0);
}

.pswp__bg {
  opacity: .005;
  background: var(--pswp-bg);
}

.pswp, .pswp__scroll-wrap {
  overflow: hidden;
}

/* Remove grain static effect */
body::before {
    display: none !important;
}
}

.pswp__scroll-wrap, .pswp__bg, .pswp__container, .pswp__item, .pswp__content, .pswp__img, .pswp__zoom-wrap {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.pswp__img, .pswp__zoom-wrap {
  width: auto;
  height: auto;
}

.pswp--click-to-zoom.pswp--zoom-allowed .pswp__img {
  cursor: var(--cursor-zoom-in-svg-url) 28 28, zoom-in;
}

.pswp--click-to-zoom.pswp--zoomed-in .pswp__img {
  cursor: grab;
}

.pswp--click-to-zoom.pswp--zoomed-in .pswp__img:active {
  cursor: grabbing;
}

.pswp--no-mouse-drag.pswp--zoomed-in .pswp__img, .pswp--no-mouse-drag.pswp--zoomed-in .pswp__img:active, .pswp__img {
  cursor: var(--cursor-zoom-out-svg-url) 28 28, zoom-out;
}

.pswp__container, .pswp__img, .pswp__button {
  -webkit-user-select: none;
  user-select: none;
}

.pswp__item {
  z-index: 1;
  overflow: hidden;
}

.pswp__hidden {
  display: none !important;
}

.pswp__content {
  pointer-events: none;
}

.pswp__content > * {
  pointer-events: auto;
}

.pswp__error-msg-container {
  display: grid;
}

.pswp__error-msg {
  color: var(--pswp-error-text-color);
  margin: auto;
  font-size: 1em;
  line-height: 1;
}

.pswp .pswp__hide-on-close {
  opacity: .005;
  will-change: opacity;
  transition: opacity var(--pswp-transition-duration) cubic-bezier(.4, 0, .22, 1);
  z-index: 10;
  pointer-events: none;
}

.pswp--ui-visible .pswp__hide-on-close {
  opacity: 1;
  pointer-events: auto;
}

.pswp__button {
  -webkit-touch-callout: none;
  display: block;
  position: relative;
  overflow: hidden;
}

.pswp__button:disabled {
  opacity: .3;
  cursor: auto;
}

.pswp__button svg {
  display: block;
}

.pswp__top-bar, .pswp__bottom-bar {
  z-index: 10;
  flex-direction: row;
  justify-content: flex-end;
  width: 100%;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none !important;
}

.pswp__top-bar > *, .pswp__bottom-bar > * {
  pointer-events: auto;
  will-change: opacity;
}

.pswp__bottom-bar {
  justify-content: center;
  top: auto;
  bottom: 24px;
}

.pswp__bottom-bar .pagination {
  border-radius: var(--rounded-full);
}

.pswp__button--close {
  margin-block-start: 24px;
  margin-inline-end: 24px;
  transition: transform .2s;
}

.pswp__preloader {
  display: none;
}

@media screen and (min-width: 700px) {
  .pswp__bottom-bar {
    bottom: 48px;
  }

  .pswp__button--close {
    margin-block-start: 48px;
    margin-inline-end: 48px;
  }
}

@media screen and (pointer: fine) {
  .pswp__button--close:hover {
    transform: rotate(90deg);
  }
}

.accordion {
  --accordion-spacing: var(--spacing-5);
  box-sizing: content-box;
  border-block-start-width: 1px;
  border-block-end-width: 1px;
}

.accordion__toggle {
  gap: var(--spacing-2);
  flex-grow: 1;
  justify-content: space-between;
  align-items: center;
  padding-block-start: var(--accordion-spacing);
  padding-block-end: var(--accordion-spacing);
  display: flex;
}

.accordion__content {
  margin-block-end: var(--accordion-spacing);
  padding-inline-end: var(--spacing-6);
  transform: translateY(-4px);
}

.accordion__content:has(.image-filter-list) {
  padding-inline-end: 0;
}

.accordion--lg {
  --accordion-spacing: var(--spacing-8);
}

.accordion + .accordion {
  border-block-start-width: 0;
}

.accordion-box {
  padding: var(--spacing-1) var(--spacing-6);
}

.accordion-box > :first-child {
  border-block-start-width: 0;
}

.accordion-box > :last-child {
  border-block-end-width: 0;
}

@media screen and (min-width: 1000px) {
  .accordion-box {
    padding: var(--spacing-5) var(--spacing-12);
  }
}

.blog-post-card {
  --blog-post-card-badge-spacing: var(--spacing-3);
  --blog-post-card-figure-gap: var(--spacing-5);
  --blog-post-card-meta-gap: var(--spacing-1) var(--spacing-5);
  gap: var(--blog-post-card-figure-gap);
  align-content: start;
  display: grid;
  overflow: hidden;
}

.blog-post-card__figure {
  display: block;
  position: relative;
  overflow: hidden;
}

.blog-post-card__figure > .badge {
  z-index: 1;
  position: absolute;
  top: var(--blog-post-card-badge-spacing);
}

.blog-post-card__figure > .badge:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: var(--blog-post-card-badge-spacing);
}

.blog-post-card__figure > .badge:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: var(--blog-post-card-badge-spacing);
}

.blog-post-card__meta {
  gap: var(--blog-post-card-meta-gap);
  flex-wrap: wrap;
  display: flex;
}

.blog-post-card--featured {
  grid-column: 1 / -1;
  gap: 0;
}

.blog-post-card--featured .blog-post-card__info {
  padding: var(--spacing-5);
}

@media screen and (min-width: 700px) {
  .blog-post-card {
    --blog-post-card-badge-spacing: var(--spacing-4);
    --blog-post-card-figure-gap: var(--spacing-6);
    --blog-post-card-meta-gap: var(--spacing-2) var(--spacing-6);
  }

  .blog-post-card--featured .blog-post-card__info {
    padding: var(--spacing-12);
  }
}

@media screen and (min-width: 1000px) {
  .blog-post-card--featured {
    grid-template-columns: 1fr .7fr;
    display: grid;
  }
}

@media screen and (min-width: 1150px) {
  .blog-post-card--featured {
    grid-template-columns: 1fr .54fr;
  }

  .blog-post-card--featured .blog-post-card__info {
    padding: var(--spacing-16);
  }
}

.blog-posts {
  --blog-posts-gap: var(--spacing-8) var(--grid-gutter);
  grid: var(--blog-posts-grid);
  gap: var(--blog-posts-gap);
  display: grid;
}

@media screen and (min-width: 700px) {
  .blog-posts {
    --blog-posts-gap: var(--spacing-12) var(--grid-gutter);
  }
}

@media screen and (min-width: 1000px) {
  .blog-posts {
    --blog-posts-gap: var(--spacing-12);
  }
}

@media screen and (min-width: 1400px) {
  .blog-posts {
    --blog-posts-gap: min(var(--section-inner-spacing-inline), var(--spacing-20));
  }
}

.button, .btn {
  --button-background: var(--button-background-primary) / var(--button-background-opacity, 1);
  --button-text-color: var(--button-text-primary);
  --button-outline-color: var(--button-background-primary);
  -webkit-appearance: none;
  appearance: none;
  border-radius: var(--rounded-button);
  background-color: rgb(var(--button-background));
  color: rgb(var(--button-text-color));
  text-align: center;
  font-size: var(--text-sm);
  letter-spacing: var(--text-letter-spacing);
  padding-block-start: var(--spacing-2-5);
  padding-block-end: var(--spacing-2-5);
  padding-inline-start: var(--spacing-5);
  padding-inline-end: var(--spacing-5);
  font-weight: 400;
  line-height: 1.6;
  transition: background-color .15s ease-in-out, color .15s ease-in-out, box-shadow .15s ease-in-out;
  display: inline-block;
  position: relative;
}

.button--sm {
  font-size: var(--text-xs);
  padding-block-start: var(--spacing-2);
  padding-block-end: var(--spacing-2);
  line-height: 1.7;
}

.button--lg {
  font-size: var(--text-base);
  padding-block-start: .8125rem;
  padding-block-end: .8125rem;
  padding-inline-start: var(--spacing-6);
  padding-inline-end: var(--spacing-6);
}

.button--xl {
  font-size: var(--text-base);
  padding-block-start: var(--spacing-4);
  padding-block-end: var(--spacing-4);
  padding-inline-start: var(--spacing-8);
  padding-inline-end: var(--spacing-8);
}

.button--secondary {
  --button-background: var(--button-background-secondary) / var(--button-background-opacity, 1);
  --button-text-color: var(--button-text-secondary);
  --button-outline-color: var(--button-background-secondary);
}

.button--subdued {
  --button-background: var(--text-color) / .1 !important;
  --button-text-color: var(--text-color) !important;
  --button-outline-color: var(--text-color) !important;
}

.button--outline {
  color: rgb(var(--button-outline-color) / var(--button-background-opacity, 1));
  background: none;
  box-shadow: inset 0 0 0 2px;
}

.button__loader, .button__feedback {
  gap: var(--spacing-1-5);
  opacity: 0;
  pointer-events: none;
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.button__loader > * {
  width: var(--spacing-1-5);
  height: var(--spacing-1-5);
  border-radius: var(--rounded-full);
  background: currentColor;
}

@media screen and (min-width: 700px) {
  .button, .btn {
    padding-block-start: var(--spacing-3);
    padding-block-end: var(--spacing-3);
    padding-inline-start: var(--spacing-6);
    padding-inline-end: var(--spacing-6);
  }

  .button--sm {
    padding-block-start: var(--spacing-2);
    padding-block-end: var(--spacing-2);
    padding-inline-start: var(--spacing-5);
    padding-inline-end: var(--spacing-5);
  }

  .button--lg {
    padding-block-start: var(--spacing-3-5);
    padding-block-end: var(--spacing-3-5);
    padding-inline-start: var(--spacing-8);
    padding-inline-end: var(--spacing-8);
  }

  .button--xl {
    padding-block-start: 1.075rem;
    padding-block-end: 1.075rem;
    padding-inline-start: var(--spacing-10);
    padding-inline-end: var(--spacing-10);
  }
}

.back-button {
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--rounded-button);
  opacity: .7;
  border-width: 1px;
  transition: all .2s ease-in-out;
  display: flex;
}

@media screen and (pointer: fine) {
  .back-button:hover {
    opacity: 1;
  }
}

@media screen and (min-width: 700px) {
  .back-button {
    padding: var(--spacing-3) var(--spacing-6);
  }
}

.circle-button {
  width: var(--spacing-12);
  height: var(--spacing-12);
  border-radius: var(--rounded-full);
  place-items: center;
  transition: opacity .15s ease-in;
  display: grid;
}

.circle-button[disabled] {
  opacity: .5;
  pointer-events: none;
}

.circle-button--fill {
  background: rgb(var(--background-primary));
  color: rgb(var(--text-primary));
}

.circle-button--bordered {
  border: 2px solid;
}

.circle-button--sm {
  width: var(--spacing-10);
  height: var(--spacing-10);
}

.circle-button--lg {
  width: var(--spacing-14);
  height: var(--spacing-14);
}

.animated-arrow {
  pointer-events: none;
  place-items: center;
  display: grid;
}

.animated-arrow:before, .animated-arrow:after {
  content: "";
  grid-area: 1 / -1;
  transition: all .2s ease-in-out;
}

.animated-arrow:before {
  border-block-start-width: 2px;
  border-inline-end-width: 2px;
  transform: rotate(calc(var(--transform-logical-flip) * 45deg));
  border-color: currentColor;
  width: .5rem;
  height: .5rem;
  position: relative;
}

.animated-arrow:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: -1px;
}

.animated-arrow:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: -1px;
}

.animated-arrow:after {
  width: var(--spacing-2-5);
  height: var(--spacing-0-5);
  transform-origin: center;
  opacity: 0;
  background: currentColor;
  transform: scaleX(.5);
}

@media screen and (pointer: fine) {
  .group:hover .animated-arrow:before {
    transform: translateX(calc(var(--transform-logical-flip) * var(--spacing-0-5))) rotate(calc(var(--transform-logical-flip) * 45deg));
  }

  .group:hover .animated-arrow:after {
    opacity: 1;
    transform: scaleX(1);
  }
}

.animated-arrow--reverse {
  transform: rotate(180deg);
}

.circle-chevron {
  width: var(--spacing-6);
  height: var(--spacing-6);
  border-radius: var(--rounded-full);
  background: rgb(var(--text-color) / .1);
  flex-shrink: 0;
  place-items: center;
  transition: all .2s ease-in-out;
  display: grid;
}

.circle-chevron[disabled] {
  opacity: .5;
}

.group[aria-expanded="true"] .circle-chevron.group-expanded\:colors:not([disabled]) {
  background: rgb(var(--text-color));
  color: rgb(var(--background));
}

.group[aria-expanded="true"] .circle-chevron.group-expanded\:rotate {
  transform: rotate(calc(var(--transform-logical-flip) * 180deg));
}

@media screen and (pointer: fine) {
  .group:hover .circle-chevron.group-hover\:colors:not([disabled]), .circle-chevron.hover\:colors:hover:not([disabled]) {
    background: rgb(var(--text-color));
    color: rgb(var(--background));
  }
}

scroll-carousel {
  position: relative;
}

.content-over-media {
  grid-template: 0 minmax(0, 1fr) 0 / minmax(0, 1fr) minmax(0, min(var(--container-max-width), 100% - var(--content-over-media-gap, var(--content-over-media-column-gap, var(--container-gutter))) * 2)) minmax(0, 1fr);
  gap: var(--content-over-media-row-gap, var(--content-over-media-gap, var(--container-gutter))) var(--content-over-media-column-gap, var(--content-over-media-gap, var(--container-gutter)));
  place-items: center;
  display: grid;
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
}

.content-over-media:before {
  content: "";
  background: rgb(var(--content-over-media-overlay));
  border-radius: inherit;
  z-index: 1;
  pointer-events: none;
  transition: background .2s ease-in-out;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.content-over-media > :is(picture, img, video, iframe, svg, video-media) {
  height: var(--content-over-media-height, auto);
  transform-origin: top;
  overflow-wrap: anywhere;
  object-fit: cover;
  object-position: center;
  border-radius: inherit;
  -webkit-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  grid-area: 1 / 1 / span 3 / span 3;
  place-self: stretch stretch;
  width: 100%;
  min-height: 100%;
  max-height: 100%;
  position: relative;
}

.content-over-media > :is(picture, img, video, iframe, svg, video-media) > * {
  border-radius: inherit;
}

.content-over-media > :not(img, video, iframe, svg, video-media, picture) {
  max-width: var(--content-over-media-content-max-width, 780px);
  z-index: 1;
  grid-area: 2 / 2 / auto / span 1;
  position: relative;
}

[allow-transparent-header] :is(.content-over-media > :not(img, video, iframe, svg, video-media, picture)) {
  padding-block-start: calc(var(--header-height) * var(--section-is-first));
}

.content-over-media > video-media:not([autoplay]) ~ * {
  transition: opacity .2s ease-in-out, visibility .2s ease-in-out;
}

.content-over-media > video-media:not([autoplay])[loaded] ~ * {
  opacity: 0;
  visibility: hidden;
}

.content-over-media:has( > video-media[loaded]:not([autoplay])):before {
  background: none;
  transition-delay: .1s;
}

.content-over-media--sm {
  --content-over-media-height: 375px;
}

.content-over-media--md {
  --content-over-media-height: 480px;
}

.content-over-media--lg {
  --content-over-media-height: 560px;
}

.content-over-media--fill {
  --content-over-media-height: calc(100vh - var(--sticky-area-height));
}

.shopify-section:first-child [allow-transparent-header] .content-over-media--fill {
  --content-over-media-height: calc(100vh - var(--announcement-bar-height, 0px));
}

@supports (height: 100svh) {
  .content-over-media--fill {
    --content-over-media-height: calc(100svh - var(--sticky-area-height));
  }

  .shopify-section:first-child [allow-transparent-header] .content-over-media--fill {
    --content-over-media-height: calc(100svh - var(--announcement-bar-height, 0px));
  }
}

@media screen and (min-width: 700px) {
  .content-over-media--sm {
    --content-over-media-height: 400px;
  }

  .content-over-media--md {
    --content-over-media-height: 460px;
  }

  .content-over-media--lg {
    --content-over-media-height: 560px;
  }
}

@media screen and (min-width: 1400px) {
  .product-gallery__zoom {
    opacity: 0;
    position: absolute;
    top: auto;
    bottom: var(--spacing-6);
    transform: scale(.8);
  }

  .product-gallery__zoom:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--spacing-6);
  }

  .product-gallery__zoom:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--spacing-6);
  }
}

/* Fixed navigation arrows - Always visible */
.product-gallery__nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  opacity: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.2s ease-in-out;
}

.product-gallery__nav-arrow--prev {
  left: var(--spacing-6);
}

.product-gallery__nav-arrow--next {
  right: var(--spacing-6);
}

/* Hide the cursor-based navigation */
custom-cursor.product-gallery__cursor {
  display: none !important;
}

@media screen and (min-width: 1600px) {
  .content-over-media--md {
    --content-over-media-height: 560px;
  }

  .content-over-media--lg {
    --content-over-media-height: 720px;
  }
}

.page-dots {
  justify-content: center;
  gap: var(--spacing-2-5) var(--spacing-4);
  flex-wrap: wrap;
  display: flex;
}

.page-dots > * {
  width: var(--spacing-1-5);
  height: var(--spacing-1-5);
  border-radius: var(--rounded-full);
  opacity: .3;
  background: currentColor;
  transition: opacity .2s ease-in-out;
}

.page-dots > *[aria-current="true"] {
  opacity: 1;
}

.page-dots--blurred {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--rounded-full);
  background: rgb(var(--background) / .7);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

.stretching-dots {
  align-items: center;
  gap: var(--spacing-5);
  display: flex;
}

.stretching-dots > * {
  width: var(--spacing-2);
  height: var(--spacing-2);
  border-radius: var(--spacing-2);
  opacity: .5;
  background: currentColor;
  transition: opacity .5s cubic-bezier(.34, 1.56, .64, 1), width .5s cubic-bezier(.34, 1.56, .64, 1);
}

.stretching-dots > *[aria-current="true"] {
  width: var(--spacing-6);
  opacity: 1;
}

.numbered-dots {
  align-items: center;
  gap: var(--spacing-2);
  display: flex;
}

.numbered-dots__item {
  width: var(--spacing-8);
  height: var(--spacing-8);
  will-change: transform;
  border-radius: 100%;
  place-items: center;
  font-weight: bold;
  transition: color .2s;
  display: grid;
}

.numbered-dots__item > * {
  grid-area: 1 / -1;
}

.numbered-dots__item svg {
  width: inherit;
  height: inherit;
  transform: rotate(-90deg);
}

.numbered-dots__item circle {
  fill: none;
  stroke: currentColor;
  stroke-linecap: butt;
  stroke-opacity: .3;
  stroke-width: 2px;
}

.numbered-dots__item[aria-current="true"] circle:last-child {
  --radius: 15;
  --circumference: calc(2px * (22 / 7) * var(--radius));
  stroke-opacity: 1;
  stroke-dasharray: calc(var(--circumference) * var(--progress, 1)), var(--circumference);
}

.prev-next-buttons {
  align-items: center;
  gap: var(--spacing-4);
  display: flex;
}

:is(.drawer, .popover):not(:defined) {
  display: none;
}

:is(.drawer, .popover)::part(outside-close-button), :is(.drawer, .popover)::part(close-button) {
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  border: none;
  padding: 0;
}

:is(.drawer, .popover)::part(outside-close-button) {
  width: var(--spacing-12);
  height: var(--spacing-12);
  bottom: 100%;
  left: calc(50% - var(--spacing-6));
  background: rgb(var(--dialog-background));
  color: rgb(var(--text-color));
  border-radius: var(--rounded-full);
  z-index: 1;
  place-items: center;
  margin-block-end: var(--spacing-4);
  margin-inline-start: auto;
  margin-inline-end: auto;
  display: grid;
  position: absolute;
}

:is(.drawer, .popover)::part(close-button) {
  color: currentColor;
  background: none;
  margin-inline-start: auto;
  display: none;
}

@media screen and (min-width: 700px) {
  :is(.drawer, .popover)::part(outside-close-button) {
    display: none;
  }

  :is(.drawer, .popover)::part(close-button) {
    display: grid;
  }
}

.popover {
  --background: var(--dialog-background);
  --text-color: var(--text-primary);
  --popover-title-padding: var(--spacing-5);
  --popover-body-padding: var(--spacing-4) 0;
  --popover-content-max-height: 75vh;
  --popover-content-max-width: none;
  --popover-anchor-block-spacing: var(--spacing-4);
  --popover-anchor-inline-spacing: 0;
  visibility: hidden;
  padding: var(--spacing-2);
  z-index: 999;
  width: 100%;
  display: none;
  position: fixed;
}

.popover::part(overlay) {
  cursor: var(--cursor-close-svg-url) 28 28, auto;
  background: #0006;
  min-height: 100lvh;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.popover::part(title) {
  padding: var(--popover-title-padding);
  border-block-end: 1px solid rgb(var(--border-color));
  text-align: center;
}

.popover::part(content) {
  max-height: var(--popover-content-max-height);
  max-width: var(--popover-content-max-width);
  background: rgb(var(--dialog-background));
  color: rgb(var(--text-color));
  border-radius: 8px;
  flex-direction: column;
  height: 100%;
  display: flex;
  position: relative;
}

.popover::part(body) {
  overscroll-behavior-y: contain;
  padding: var(--popover-body-padding);
  text-align: center;
  overflow-y: auto;
}

@media screen and (min-width: 1000px) {
  .popover {
    --popover-content-max-height: 350px;
    --popover-content-max-width: 380px;
    z-index: 10;
    width: auto;
    padding: 0;
    position: absolute;
  }

  .popover::part(content) {
    border-radius: var(--rounded-input);
    box-shadow: var(--shadow-md);
    border: 1px solid rgb(var(--border-color));
    width: max-content;
  }

  .popover::part(title), .popover::part(overlay) {
    display: none;
  }

  .popover::part(body) {
    text-align: start;
  }
}

.popover-listbox__option {
  justify-content: center;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-6);
  color: rgb(var(--text-color) / .7);
  width: 100%;
  transition: color .2s ease-in-out;
  display: flex;
}

.popover-listbox__option.is-disabled {
  text-decoration: line-through;
}

.popover-listbox__option[aria-selected="true"], .popover-listbox__option:hover {
  color: rgb(var(--text-color));
}

.popover-listbox__option:has(:checked) {
  color: rgb(var(--text-color));
}

@media screen and (min-width: 1000px) {
  .popover-listbox__option {
    justify-content: start;
    padding-block-start: var(--spacing-1-5);
    padding-block-end: var(--spacing-1-5);
    padding-inline-end: var(--spacing-10);
  }

  .popover-listbox--sm .popover-listbox__option {
    line-height: 1.7;
    font-size: var(--text-sm);
    padding-block-start: var(--spacing-1);
    padding-block-end: var(--spacing-1);
  }
}

.drawer {
  --container-outer-width: var(--spacing-6);
  --background: var(--dialog-background);
  --text-color: var(--text-primary);
  --drawer-header-padding: var(--spacing-5);
  --drawer-body-padding: var(--spacing-5) var(--spacing-6);
  --drawer-footer-padding: var(--spacing-6);
  --drawer-content-max-height: none;
  visibility: hidden;
  padding: var(--spacing-2);
  z-index: 999;
  width: 100%;
  position: fixed;
  right: 0;
}

.drawer::part(overlay) {
  background: #0006;
  min-height: 100lvh;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.drawer.show-close-cursor::part(overlay) {
  cursor: var(--cursor-close-svg-url) 28 28, auto;
}

.drawer::part(content) {
  max-height: var(--drawer-content-max-height);
  border-radius: var(--rounded-sm);
  background: rgb(var(--dialog-background));
  color: rgb(var(--text-color));
  flex-direction: column;
  height: 100%;
  display: flex;
  position: relative;
}

.drawer::part(header) {
  padding: var(--drawer-header-padding);
}

.drawer::part(body) {
  padding: var(--drawer-body-padding);
  overscroll-behavior-y: contain;
  flex: auto;
  align-items: start;
  display: grid;
  overflow-y: auto;
}

.drawer::part(footer) {
  padding: var(--drawer-footer-padding);
  border-block-start: 1px solid rgb(var(--border-color));
  margin-block-start: auto;
  position: relative;
}

.drawer > [is="close-button"] {
  display: none;
  position: absolute;
  top: 1.5rem;
}

.drawer > [is="close-button"]:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 1.5rem;
}

.drawer > [is="close-button"]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 1.5rem;
}

@media screen and (max-width: 699px) {
  .drawer {
    --drawer-content-max-height: 75vh;
  }

  .drawer::part(header) {
    text-align: center;
    border-bottom: 1px solid rgb(var(--border-color));
  }
}

@media screen and (min-width: 700px) {
  .drawer {
    --container-outer-width: var(--spacing-10);
    --drawer-header-padding: var(--spacing-8) var(--spacing-10);
    --drawer-body-padding: var(--spacing-8) var(--spacing-10);
    --drawer-footer-padding: var(--spacing-8) var(--spacing-10);
    padding: var(--spacing-4);
    width: 510px;
    height: 100%;
  }

  .drawer::part(header) {
    align-items: center;
    gap: var(--spacing-4);
    display: flex;
    position: relative;
  }

  .drawer[header-bordered]::part(header) {
    border-bottom: 1px solid rgb(var(--border-color));
  }

  .drawer:not([header-bordered])::part(header) {
    padding-block-end: 0;
  }

  .drawer:not([header-bordered])::part(header):after {
    content: "";
    height: var(--spacing-8);
    background: linear-gradient(to bottom, rgb(var(--dialog-background)), rgb(var(--dialog-background) / .6) 50%, rgb(var(--dialog-background) / 0));
    z-index: 1;
    width: 100%;
    position: absolute;
    top: 100%;
    left: 0;
  }

  .drawer > [is="close-button"] {
    display: grid;
    top: 2.25rem;
  }

  .drawer > [is="close-button"]:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 2.25rem;
  }

  .drawer > [is="close-button"]:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 2.25rem;
  }

  .drawer--lg {
    width: 680px;
  }
}

.banner {
  align-items: start;
  gap: var(--spacing-3-5);
  padding: var(--spacing-3-5);
  border-radius: var(--rounded-xs);
  font-size: var(--text-sm);
  color: rgb(var(--banner-color));
  background: rgb(var(--banner-background));
  grid-template-columns: max-content auto;
  display: grid;
}

.banner--error {
  --banner-background: var(--error-background);
  --banner-color: var(--error-text);
}

.banner--success {
  --banner-background: var(--success-background);
  --banner-color: var(--success-text);
}

.banner--warning {
  --banner-background: var(--warning-background);
  --banner-color: var(--warning-text);
}

.banner--with-icon > .button {
  grid-column-start: 2;
  justify-self: start;
}

@media screen and (min-width: 700px) {
  .banner--with-icon {
    grid-template-columns: max-content auto max-content;
    align-items: center;
  }

  .banner--with-icon svg {
    --icon-offset: 0;
  }

  .banner--with-icon > .button {
    grid-column-start: 3;
  }
}

.badge {
  --badge-background: var(--text-color) / .05;
  --badge-color: var(--text-color);
  align-items: center;
  gap: var(--spacing-1-5);
  padding: 3px var(--spacing-2);
  background: rgb(var(--badge-background));
  color: rgb(var(--badge-color));
  border-radius: var(--rounded-button);
  width: max-content;
  font-size: small; 
  /* font-weight: bold; */
  display: flex;
}

.badge--primary {
  --badge-background: var(--primary-badge-background);
  --badge-color: var(--primary-badge-text);
}

.badge--sold-out {
  --badge-background: var(--sold-out-badge-background);
  --badge-color: var(--sold-out-badge-text);
}

.badge--on-sale {
  --badge-background: var(--on-sale-badge-background);
  --badge-color: var(--on-sale-badge-text);
}

.badge--current {
  --badge-background: var(--text-color) / .12;
  --badge-color: var(--text-color);
}

.badge--lg {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--text-sm);
}

@media screen and (min-width: 700px) {
  .badge:not(.badge--lg) {
    font-size: var(--text-xs);
    padding-block-start: var(--spacing-0-5);
    padding-block-end: var(--spacing-0-5);
  }
}

.progress-bar {
  height: var(--spacing-1);
  border-radius: var(--rounded-full);
  background: rgb(var(--text-color) / .3);
  overflow: hidden;
}

.progress-bar:before {
  content: "";
  height: inherit;
  background: rgb(var(--accent));
  transform-origin: var(--transform-origin-start);
  transform: scaleX(var(--progress, 0));
  transition: transform .5s ease-in-out;
  display: block;
}

.scrollbar {
  align-items: center;
  gap: var(--spacing-10);
  color: rgb(var(--text-color));
  display: flex;
}

.scrollbar__progress {
  height: var(--spacing-0-5);
  background: rgb(var(--text-color) / .1);
  width: 100%;
  display: block;
  position: relative;
}

.scrollbar__progress:before {
  content: "";
  transform-origin: var(--transform-origin-start);
  transform: scaleX(var(--scroll-progress));
  background: rgb(var(--text-color));
  will-change: transform;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.scrollbar__buttons {
  display: none;
}

@media screen and (min-width: 1000px) {
  .scrollbar__buttons {
    gap: var(--spacing-4);
    display: flex;
  }
}

.count-bubble {
  --size: var(--spacing-4);
  width: var(--size);
  height: var(--size);
  border-radius: var(--rounded-full);
  background: rgb(var(--button-background-primary));
  color: rgb(var(--button-text-primary));
  place-items: center;
  min-width: min-content;
  padding-inline-start: var(--spacing-1);
  padding-inline-end: var(--spacing-1);
  font-size: 9px;
  font-weight: bold;
  line-height: 1;
  display: grid;
}

.count-bubble--md, .count-bubble--lg {
  --size: var(--spacing-5);
  font-size: var(--text-xs);
}

@media screen and (min-width: 700px) {
  .count-bubble--md {
    --size: var(--spacing-6);
  }

  .count-bubble--lg {
    --size: var(--spacing-7);
  }
}

.text-with-bubble {
  position: relative;
}

.text-with-bubble > .count-bubble {
  margin-inline-start: var(--spacing-2);
  position: absolute;
  top: 0;
}

.text-with-bubble > .count-bubble:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 100%;
}

.text-with-bubble > .count-bubble:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 100%;
}

.pill-loader {
  background: rgb(var(--background-primary));
  border-radius: var(--rounded-full);
  visibility: hidden;
  width: 2rem;
  height: 1rem;
  display: grid;
}

.pill-loader > * {
  grid-area: 1 / -1;
  place-self: center;
}

.loader-dots {
  justify-items: center;
  gap: .25rem;
  display: flex;
}

.loader-dots > * {
  border-radius: var(--rounded-full);
  background: currentColor;
  width: .25rem;
  height: .25rem;
}

.loader-checkmark {
  opacity: 0;
}

.form {
  align-content: start;
  gap: var(--spacing-4);
  display: grid;
}

.fieldset, .input-row {
  gap: var(--input-gap);
  display: grid;
}

.fieldset-link {
  margin-block-start: var(--spacing-2);
  margin-block-end: var(--spacing-2);
}

.fieldset-with-submit {
  align-items: start;
  gap: var(--spacing-4);
  display: grid;
}

@media screen and (min-width: 700px) {
  .form:not(.form--tight) {
    gap: var(--spacing-6);
  }

  .input-row {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .fieldset-link {
    margin-block-start: 0;
    margin-block-end: var(--spacing-2);
  }

  .fieldset-with-submit {
    grid-template-columns: minmax(0, 1fr) max-content;
  }
}

.input, .textarea, .select {
  -webkit-appearance: none;
  appearance: none;
  height: var(--input-height);
  border-radius: var(--rounded-input);
  background: rgb(var(--input-background, transparent));
  color: rgb(var(--input-text-color, var(--text-color)));
  text-align: start;
  border-width: 1px;
  width: 100%;
  padding-inline-start: var(--input-padding-inline);
  padding-inline-end: var(--input-padding-inline);
}

:is(.input:focus, .textarea:focus, .select:focus-visible) {
  border-color: currentColor;
  outline: none;
  box-shadow: inset 0 0 0 1px;
}

.input.is-floating, .select.is-floating {
  height: calc(var(--input-height)  + .625rem);
  padding-block-start: var(--spacing-4);
}

.textarea {
  vertical-align: top;
  height: auto;
  padding-block-start: var(--spacing-4);
  padding-block-end: var(--spacing-4);
}

.textarea.is-floating {
  padding-block-start: var(--spacing-6);
  padding-block-end: var(--spacing-2);
}

.select {
  gap: var(--spacing-4);
  justify-content: space-between;
  align-items: center;
  padding-inline-end: calc(var(--input-padding-inline) * 2);
  display: flex;
}

.select-chevron {
  position: absolute;
  top: calc(50% - 3.5px);
}

.select-chevron:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: var(--input-padding-inline);
}

.select-chevron:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: var(--input-padding-inline);
}

.form-control {
  text-align: start;
  position: relative;
}

.block-label {
  width: -moz-fit-content;
  width: fit-content;
  margin-block-end: var(--spacing-2);
  display: block;
}

.floating-label {
  height: calc(var(--input-height)  + .625rem);
  top: 0;
  opacity: .5;
  pointer-events: none;
  transform-origin: var(--transform-origin-start);
  grid-auto-flow: column;
  align-items: center;
  column-gap: .5rem;
  margin-inline-start: var(--spacing-4);
  transition: transform .2s ease-in-out;
  display: grid;
  position: absolute;
}

.floating-label:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 1px;
}

.floating-label:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 1px;
}

:-webkit-any(:-webkit-any(.input, .textarea):focus ~ .floating-label, :-webkit-any(.input, .textarea):not(:placeholder-shown) ~ .floating-label, :-webkit-any(.input, .textarea, .select):-webkit-autofill ~ .floating-label, .select:valid ~ .floating-label) {
  transform: scale(.65) translateY(calc(-1 * var(--spacing-4)));
}

:is(:is(.input, .textarea):focus ~ .floating-label, :is(.input, .textarea):not(:placeholder-shown) ~ .floating-label, :is(.input, .textarea, .select):autofill ~ .floating-label, .select:valid ~ .floating-label) {
  transform: scale(.65) translateY(calc(-1 * var(--spacing-4)));
}

:-webkit-any(.input, .textarea, .select):-webkit-autofill ~ .floating-label {
  color: #000;
}

:is(.input, .textarea, .select):autofill ~ .floating-label {
  color: #000;
}

.self-submit-button {
  top: 50%;
  position: absolute;
  transform: translateY(-50%);
}

.self-submit-button:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: var(--spacing-4);
}

.self-submit-button:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: var(--spacing-4);
}

@media screen and (min-width: 700px) {
  .floating-label {
    margin-inline-start: var(--spacing-5);
  }

  .self-submit-button:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: var(--spacing-5);
  }

  .self-submit-button:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: var(--spacing-5);
  }
}

.checkbox-container {
  align-items: baseline;
  display: flex;
}

.checkbox {
  -webkit-appearance: none;
  appearance: none;
  width: var(--spacing-4);
  height: var(--spacing-4);
  background: rgb(var(--text-color) / .15) var(--checkmark-svg-url) no-repeat center;
  background-size: 0;
  border-radius: 2px;
  flex-shrink: 0;
  transition: background-color .2s ease-in-out;
  position: relative;
  top: .1875rem;
  box-shadow: inset 1px 1px 2px #0000001a;
}

.checkbox:checked {
  background-color: rgb(var(--accent));
  background-size: 10px 8px;
}

.checkbox:checked ~ label {
  opacity: 1;
  transition: all .2s ease-in-out;
}

.checkbox ~ label {
  opacity: .7;
  padding-inline-start: var(--spacing-3);
}

.checkbox:disabled {
  opacity: .5;
}

.checkbox:disabled ~ label {
  color: rgb(var(--text-color) / .5);
  cursor: default;
}

.fieldset > .checkbox-container {
  margin-block-start: var(--spacing-1);
}

.checkbox-list {
  gap: var(--spacing-2);
  display: grid;
}

@media screen and (min-width: 700px) {
  .checkbox {
    top: var(--spacing-0-5);
  }
}

.switch {
  -webkit-appearance: none;
  appearance: none;
  height: var(--spacing-5);
  width: var(--spacing-10);
  border-radius: var(--rounded-full);
  background: rgb(var(--text-color) / .15);
  padding: 3px;
  transition: background .2s ease-in-out;
  display: grid;
}

.switch:after {
  content: "";
  width: var(--spacing-3-5);
  height: var(--spacing-3-5);
  background: rgb(var(--background));
  border-radius: var(--rounded-full);
  transition: transform .2s ease-in-out;
  box-shadow: 0 1px 1px #0003;
}

.switch:checked {
  background: rgb(var(--accent));
}

.switch:checked:after {
  transform: translateX(calc(var(--transform-logical-flip) * var(--spacing-5)));
}

.horizontal-product {
  --horizontal-product-image-size: var(--spacing-16);
  align-items: flex-start;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  min-width: 0;
  display: flex;
}

.horizontal-product__image {
  width: var(--horizontal-product-image-size);
  flex: 0 0 var(--horizontal-product-image-size);
}

.horizontal-product__info {
  gap: var(--spacing-2);
  flex-grow: 1;
  justify-content: space-between;
  align-items: center;
  display: grid;
}

@media screen and (min-width: 700px) {
  .horizontal-product {
    --horizontal-product-image-size: var(--spacing-20);
    gap: var(--spacing-5);
    align-items: center;
  }

  .horizontal-product--sm {
    --horizontal-product-image-size: var(--spacing-16);
  }

  .horizontal-product__info {
    gap: var(--spacing-4);
    display: flex;
  }

  .horizontal-product__cta {
    flex-shrink: 0;
    margin-inline-end: var(--spacing-4);
  }
}

.horizontal-product-list.separate {
  gap: var(--spacing-2);
  display: grid;
}

.horizontal-product-list-carousel > .horizontal-product-list {
  grid: auto / auto-flow 73vw;
  display: grid;
}

.horizontal-product-list-carousel > .horizontal-product-list:not(.separate) {
  border-radius: var(--rounded-xs);
  border-width: 1px;
}

@media screen and (min-width: 700px) {
  .horizontal-product-list-carousel {
    border-radius: var(--rounded-xs);
    grid-template-columns: minmax(0, 1fr);
  }

  .horizontal-product-list-carousel > .horizontal-product-list {
    grid: auto / auto-flow var(--horizontal-product-width, 100%);
  }
}

.v-stack {
  display: grid;
}

.h-stack {
  align-items: center;
  display: flex;
}

.container {
  --container-inner-width: min((100vw - var(--scrollbar-width, 0px))  - var(--container-gutter) * 2, var(--container-max-width));
  --container-outer-width: calc(((100vw - var(--scrollbar-width, 0px))  - var(--container-inner-width)) / 2);
  margin-inline-start: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
  margin-inline-end: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
}

.container--narrow {
  --container-max-width: var(--container-narrow-max-width);
}

@media screen and (min-width: 700px) {
  .sm\:container {
    --container-inner-width: min((100vw - var(--scrollbar-width, 0px))  - var(--container-gutter) * 2, var(--container-max-width));
    --container-outer-width: calc(((100vw - var(--scrollbar-width, 0px))  - var(--container-inner-width)) / 2);
    margin-inline-start: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
    margin-inline-end: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
  }
}

.bleed {
  scroll-padding-inline: var(--container-outer-width);
  margin-inline-start: calc(-1 * var(--container-outer-width));
  margin-inline-end: calc(-1 * var(--container-outer-width));
  padding-inline-start: var(--container-outer-width);
  padding-inline-end: var(--container-outer-width);
  display: grid;
}

.full-bleed {
  margin-inline-start: calc(-1 * var(--container-outer-width));
  margin-inline-end: calc(-1 * var(--container-outer-width));
  display: grid;
}

@media screen and (min-width: 700px) {
  .sm\:unbleed {
    margin-inline-start: 0;
    margin-inline-end: 0;
    padding-inline-start: 0;
    padding-inline-end: 0;
    scroll-padding-inline: 0;
  }
}

@media screen and (min-width: 1000px) {
  .md\:unbleed {
    margin-inline-start: 0;
    margin-inline-end: 0;
    padding-inline-start: 0;
    padding-inline-end: 0;
    scroll-padding-inline: 0;
  }
}

@media screen and (min-width: 1150px) {
  .lg\:unbleed {
    margin-inline-start: 0;
    margin-inline-end: 0;
    padding-inline-start: 0;
    padding-inline-end: 0;
    scroll-padding-inline: 0;
  }
}

[role="main"] .shopify-section {
  --section-is-first: 0;
  --hash-difference: calc(var(--section-background-hash)  - var(--previous-section-background-hash, -1));
  --hash-difference-abs: max(var(--hash-difference), -1 * var(--hash-difference));
  --background-differs-from-previous: max(0, min(var(--hash-difference-abs, 1), 1));
  background: rgb(var(--background));
  position: relative;
}

[role="main"] .shopify-section:not(.contents) {
  display: flow-root;
}

[role="main"] .shopify-section:empty {
  display: none;
}

[role="main"] .shopify-section:first-child {
  --section-is-first: 1;
}

.section {
  --context-section-spacing-block-start: var(--section-outer-spacing-block);
  --context-section-spacing-block-end: var(--section-outer-spacing-block);
  --context-section-spacing-inline: var(--container-gutter);
  --calculated-section-spacing-block-start: var(--section-spacing-block-start, var(--section-spacing-block, var(--context-section-spacing-block-start)));
  --calculated-section-spacing-block-end: var(--section-spacing-block-end, var(--section-spacing-block, var(--context-section-spacing-block-end)));
  --calculated-section-spacing-inline: var(--section-spacing-inline, var(--context-section-spacing-inline));
  --container-inner-width: min((100vw - var(--scrollbar-width, 0px))  - var(--calculated-section-spacing-inline, 0px) * 2, var(--container-max-width));
  --container-outer-width: calc(((100vw - var(--scrollbar-width, 0px))  - var(--container-inner-width, 0px)) / 2);
  --section-stack-spacing-inline: var(--section-inner-spacing-inline, 0px);
  padding-inline-start: max(var(--calculated-section-spacing-inline), 50% - var(--container-max-width) / 2);
  padding-inline-end: max(var(--calculated-section-spacing-inline), 50% - var(--container-max-width) / 2);
}

.section--tight {
  --calculated-section-spacing-block-start: min(48px, var(--section-spacing-block-start, var(--section-spacing-block, var(--context-section-spacing-block-start))));
  --calculated-section-spacing-block-end: min(48px, var(--section-spacing-block-end, var(--section-spacing-block, var(--context-section-spacing-block-end))));
  --calculated-section-spacing-inline: min(48px, var(--section-spacing-inline, var(--context-section-spacing-inline)));
}

.section--narrow {
  --container-max-width: var(--container-narrow-max-width);
}

.section {
  padding-block-start: calc(var(--background-differs-from-previous) * var(--calculated-section-spacing-block-start));
  padding-block-end: var(--calculated-section-spacing-block-end);
}

@media screen and (min-width: 700px) {
  .section--tight {
    --calculated-section-spacing-block-start: min(48px, var(--section-spacing-block-start, var(--section-spacing-block, var(--context-section-spacing-block-start, 0px))));
    --calculated-section-spacing-block-end: min(48px, var(--section-spacing-block-end, var(--section-spacing-block, var(--context-section-spacing-block-end, 0px))));
  }

  .section-boxed {
    --context-section-spacing-block-start: min(var(--section-inner-max-spacing-block, var(--section-outer-spacing-block)));
    --context-section-spacing-block-end: min(var(--section-inner-max-spacing-block, var(--section-outer-spacing-block)));
    --context-section-spacing-inline: var(--section-inner-spacing-inline);
    --container-inner-width: calc(var(--container-max-width)  - var(--container-outer-width) * 2);
    --container-outer-width: var(--calculated-section-spacing-inline);
    border-radius: var(--rounded-lg);
    box-shadow: var(--shadow-block);
    margin-block-start: calc(var(--background-differs-from-previous) * var(--section-outer-spacing-block-start, var(--section-outer-spacing-block)));
    margin-block-end: var(--section-outer-spacin-block-end, var(--section-outer-spacing-block));
    margin-inline-start: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
    margin-inline-end: max(var(--container-gutter), 50% - var(--container-max-width) / 2);
    padding-block-start: var(--calculated-section-spacing-block-start);
    padding-inline-start: var(--calculated-section-spacing-inline);
    padding-inline-end: var(--calculated-section-spacing-inline);
  }

  .shopify-section:first-child {
    --section-outer-spacing-block-start: 24px;
  }
}

@media screen and (min-width: 1600px) {
  .section--tight {
    --calculated-section-spacing-block-start: min(64px, var(--section-spacing-block-start, var(--section-spacing-block, var(--context-section-spacing-block-start, 0px))));
    --calculated-section-spacing-block-end: min(64px, var(--section-spacing-block-end, var(--section-spacing-block, var(--context-section-spacing-block-end, 0px))));
  }
}

.section-header {
  gap: var(--spacing-4);
  justify-items: start;
  display: grid;
}

@media screen and (min-width: 1150px) {
  .section-header {
    grid-template-columns: 700px;
    justify-content: space-between;
    align-items: end;
  }

  .section-header > .text-with-icon {
    grid-column-start: 2;
  }
}

.section-stack {
  gap: var(--section-stack-spacing-block, 0px) var(--section-stack-spacing-inline, 0px);
  grid-auto-columns: minmax(0, 1fr);
  display: grid;
}

@media screen and (min-width: 1150px) {
  .section-stack--horizontal {
    justify-content: center;
    display: flex;
  }

  .section-stack--center {
    justify-items: center;
  }

  .section-stack--reverse {
    flex-direction: row-reverse;
  }

  .section-stack__intro {
    width: var(--section-stack-intro, 50%);
  }

  .section-stack__main {
    width: var(--section-stack-main, 50%);
  }
}

.page-spacer {
  margin-block-start: var(--spacing-8);
  margin-block-end: var(--section-outer-spacing-block);
}

@media screen and (min-width: 700px) {
  .page-spacer {
    margin-block-start: var(--spacing-12);
  }
}

@media screen and (min-width: 1000px) {
  .page-spacer {
    margin-block-start: var(--spacing-16);
  }
}

.box {
  padding: var(--spacing-8);
}

@media screen and (min-width: 700px) {
  .box {
    padding: var(--spacing-12);
  }
}

.empty-state {
  justify-items: center;
  gap: var(--spacing-5);
  text-align: center;
  margin-block-start: var(--spacing-12);
  margin-block-end: var(--spacing-12);
  display: grid;
}

.empty-state__icon-wrapper {
  position: relative;
}

.empty-state__icon-wrapper > .count-bubble {
  position: absolute;
  top: calc(-1 * var(--spacing-1-5));
}

.empty-state__icon-wrapper > .count-bubble:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: calc(-1 * var(--spacing-1-5));
}

.empty-state__icon-wrapper > .count-bubble:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: calc(-1 * var(--spacing-1-5));
}

@media screen and (min-width: 700px) {
  .empty-state {
    gap: var(--spacing-8);
    margin-block-start: var(--spacing-32);
    margin-block-end: var(--spacing-32);
  }

  .empty-state__icon-wrapper > svg {
    width: var(--spacing-12);
    height: var(--spacing-12);
  }

  .empty-state__icon-wrapper > .count-bubble {
    top: calc(-1 * var(--spacing-3));
  }

  .empty-state__icon-wrapper > .count-bubble:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: calc(-1 * var(--spacing-3));
  }

  .empty-state__icon-wrapper > .count-bubble:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: calc(-1 * var(--spacing-3));
  }
}

.scrollable-with-controls {
  gap: var(--spacing-6);
  grid-auto-columns: minmax(0, 1fr);
  display: grid;
}

@media screen and (min-width: 700px) {
  .scrollable-with-controls {
    gap: var(--spacing-10);
  }
}

.line-item {
  --line-item-image-width: var(--spacing-20);
  align-items: start;
  gap: var(--spacing-5);
  display: flex;
}

.line-item__media-wrapper {
  width: var(--line-item-image-width);
  min-width: var(--line-item-image-width);
  position: relative;
}

.line-item__media-wrapper > .pill-loader {
  position: absolute;
  top: calc(50% - .5rem);
}

.line-item__media-wrapper > .pill-loader:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: calc(50% - 1rem);
}

.line-item__media-wrapper > .pill-loader:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: calc(50% - 1rem);
}

.line-item__info {
  justify-items: start;
  gap: var(--spacing-1);
  display: grid;
}

.line-item__info line-item-quantity {
  margin-block-start: var(--spacing-1);
}

@media screen and (min-width: 700px) {
  .line-item {
    --line-item-image-width: var(--spacing-24);
    align-items: center;
  }

  .line-item__actions {
    margin-inline-start: auto;
  }
}

video-media, model-media {
  border-radius: inherit;
  aspect-ratio: var(--aspect-ratio, var(--default-aspect-ratio));
  position: relative;
}

@supports not (aspect-ratio: 1) {
  :is(video-media, model-media):before {
    content: "";
    padding-block-end: calc(100% / (var(--aspect-ratio, var(--default-aspect-ratio))));
    display: block;
  }

  :is(video-media, model-media) > * {
    height: 100%;
    top: 0;
    left: 0;
    position: absolute !important;
  }
}

video-media {
  --default-aspect-ratio: 16 / 9;
}

video-media[host] {
  align-items: center;
  display: grid;
}

video-media[autoplay], video-media:not([autoplay]) ~ :not(video-media) {
  pointer-events: none;
}

video-media:not([playing]) {
  cursor: pointer;
}

video-media > :is(video, iframe, img, svg) {
  border-radius: inherit;
  width: 100%;
  height: 100%;
  transition: opacity .2s ease-in-out, visibility .2s ease-in-out;
}

video-media > video[controls] {
  pointer-events: auto;
}

video-media > img, video-media > svg {
  object-fit: cover;
  object-position: center;
}

video-media > video:not(:-webkit-full-screen) {
  object-fit: cover;
  object-position: center;
}

video-media > video:not(:fullscreen) {
  object-fit: cover;
  object-position: center;
}

video-media > :is(iframe, img, svg) {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

video-media:not([loaded]) > :is(video, iframe), video-media[loaded] > img, video-media[loaded] > svg, video-media[loaded]::part(play-button) {
  opacity: 0;
  visibility: hidden;
}

video-media[suspended] ~ * {
  pointer-events: none;
}

video-media::part(play-button) {
  z-index: 1;
  will-change: transform;
  transition: transform .2s ease-in-out, opacity .2s ease-in-out, visibility .2s ease-in-out;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%)scale(1);
}

video-media:hover::part(play-button) {
  transform: translate(-50%, -50%)scale(1.1);
}

@media screen and (min-width: 700px) {
  video-media::part(play-button) {
    width: 64px;
    height: 64px;
  }
}

model-media {
  --default-aspect-ratio: 1;
}

model-media model-viewer, model-media .shopify-model-viewer-ui {
  width: 100%;
  height: 100%;
}

.video-play-button {
  --play-button-shadow-size: 12px;
  filter: drop-shadow(0 16px 50px #0000000d);
  position: relative;
}

.video-play-button:before {
  content: "";
  box-sizing: content-box;
  top: calc(-1 * var(--play-button-shadow-size));
  left: calc(-1 * var(--play-button-shadow-size));
  padding: var(--play-button-shadow-size);
  background: radial-gradient(50% 50% at 50% 50%, rgb(var(--text-color) / 0) 0%, rgb(var(--text-color)) 100%);
  opacity: .3;
  border-radius: 100%;
  width: 100%;
  height: 100%;
  animation: 2s ease-in-out infinite alternate ping;
  position: absolute;
}

@media screen and (min-width: 700px) {
  .video-play-button {
    --play-button-shadow-size: 20px;
  }

  .video-play-button svg {
    width: 5rem;
    height: 5rem;
  }
}

.order-summary__header {
  display: none;
}

.order-summary__body td {
  border-top-width: 0;
  padding-block-start: 0;
  padding-block-end: var(--spacing-4);
}

.order-summary__body td:first-child {
  width: 60%;
}

@media screen and (min-width: 700px) {
  .order-summary__header {
    display: table-header-group;
  }

  .order-summary__body td {
    padding-block-end: var(--spacing-6);
  }

  .order-summary__body tr:first-child td {
    padding-block-start: var(--spacing-8);
  }

  .order-summary__body tr:last-child td {
    padding-block-end: var(--spacing-8);
  }
}

.quantity-input {
  width: var(--quantity-input-characters-count, 1ch);
  border-radius: var(--rounded-input);
  text-align: center;
  font-size: var(--text-xs);
  -webkit-touch-callout: none;
  -webkit-appearance: none;
  background: none;
  border-width: 1px;
  outline: none;
  min-width: 44px;
  padding-block-start: var(--spacing-1);
  padding-block-end: var(--spacing-1);
}

.quantity-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quantity-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

@media screen and (min-width: 700px) {
  .quantity-input {
    min-width: 48px;
    padding-block-start: var(--spacing-2);
    padding-block-end: var(--spacing-2);
  }
}

.pagination {
  background: rgb(var(--background));
  border-radius: var(--rounded-button);
  border-width: 1px;
  justify-self: center;
  display: flex;
}

.pagination__item {
  place-content: center;
  padding-inline-start: var(--spacing-4);
  padding-inline-end: var(--spacing-4);
  display: grid;
}

.pagination__item--disabled {
  opacity: .5;
}

.pagination__current {
  padding: var(--spacing-2) var(--spacing-1);
}

@media screen and (min-width: 700px) {
  .pagination__item {
    padding-inline-start: var(--spacing-5);
    padding-inline-end: var(--spacing-5);
  }

  .pagination__current {
    padding: var(--spacing-3) var(--spacing-2);
  }
}

.range {
  -webkit-appearance: none;
  appearance: none;
  background: none;
  width: 100%;
}

.range::-webkit-slider-thumb {
  -webkit-appearance: none;
}

.range::-webkit-slider-thumb {
  background: rgb(var(--text-color));
  cursor: pointer;
  border: 2px solid rgb(var(--background));
  z-index: 1;
  box-shadow: none;
  border-radius: 100%;
  width: 16px;
  height: 16px;
  margin-block-start: -6px;
  position: relative;
}

.range::-webkit-slider-runnable-track {
  cursor: pointer;
  background: rgb(var(--text-color) / .12);
  border: none;
  border-radius: 2px;
  width: 100%;
  height: 4px;
}

.range::-moz-range-thumb {
  border: 2px solid rgb(var(--background));
  background: rgb(var(--text-color));
  cursor: pointer;
  border-radius: 100%;
  width: 12px;
  height: 12px;
}

.range::-moz-range-progress {
  cursor: pointer;
  border: none;
  border-radius: 2px;
  width: 100%;
  height: 4px;
}

.range::-moz-range-track {
  cursor: pointer;
  border: none;
  border-radius: 2px;
  width: 100%;
  height: 4px;
}

.range::-moz-range-progress {
  background-color: rgba(var(--text-color));
}

.range::-moz-range-track {
  background-color: rgb(var(--border-color));
}

@media not screen and (pointer: fine) {
  .range::-webkit-slider-thumb {
    width: 20px;
    height: 20px;
    margin-top: -9px;
  }

  .range::-moz-range-thumb {
    width: 16px;
    height: 16px;
  }
}

.range-group {
  background: linear-gradient(to var(--transform-origin-end), rgb(var(--text-color) / .12) var(--range-min), rgb(var(--text-color)) var(--range-min), rgb(var(--text-color)) var(--range-max), rgb(var(--text-color) / .12) var(--range-max));
  border-radius: 2px;
  height: 4px;
  position: relative;
}

.range-group .range {
  pointer-events: none;
  vertical-align: top;
  height: 4px;
}

.range-group .range::-webkit-slider-runnable-track {
  background: none;
}

.range-group .range::-webkit-slider-thumb {
  pointer-events: auto;
}

.range-group .range::-moz-range-progress {
  background: none;
}

.range-group .range::-moz-range-track {
  background: none;
}

.range-group .range::-moz-range-thumb {
  pointer-events: auto;
}

.range-group .range:last-child {
  position: absolute;
  top: 0;
}

.range-group .range:last-child:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.range-group .range:last-child:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.input-group {
  align-items: center;
  column-gap: 20px;
  display: flex;
}

.input-prefix {
  border-radius: min(10px, var(--rounded-button));
  border: 1px solid rgb(var(--text-color) / .12);
  flex: 1 0 0;
  justify-content: space-between;
  align-items: center;
  min-width: 0;
  padding: 10px 15px;
  display: flex;
}

.input-prefix:focus-within {
  border-color: rgb(var(--text-color));
  box-shadow: inset 0 0 0 1px rgb(var(--text-color));
}

.input-prefix > .field {
  -webkit-appearance: textfield;
  appearance: textfield;
  text-align: end;
  background: none;
  border: none;
  width: 100%;
  min-width: 0;
  margin-inline-start: 10px;
  padding: 0;
}

.input-prefix > .field:focus {
  outline: none;
}

.input-prefix > .field::-webkit-outer-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.input-prefix > .field::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.price-range {
  gap: 24px;
  padding-block-start: 4px;
  display: grid;
}

.price-range--inline {
  padding: var(--spacing-1);
  grid-template-columns: minmax(110px, auto) 380px minmax(110px, auto);
  align-items: center;
}

@media screen and not (pointer: fine) {
  .price-range {
    row-gap: 18px;
    padding-block-start: 10px;
  }
}

.product-list {
  grid: var(--product-list-grid);
  gap: var(--product-list-gap);
  scroll-padding-top: calc(var(--sticky-area-height)  + 20px);
  display: grid;
  position: relative;
}

.product-list__promo {
  grid-column: var(--product-list-promo-grid-column, span 2);
  min-height: 200px;
  display: grid;
}

@media screen and (min-width: 700px) {
  @supports (grid-template-rows: subgrid) {
    .product-list {
      grid-template-rows: auto auto;
    }

    .product-list > * {
      grid-template-rows: subgrid;
      grid-row: span 2;
      gap: 0;
    }

    .product-list__promo :is(img, video-media) {
      position: absolute;
    }

    .product-list:not(:has(.product-card--blends)) .product-list__promo .content-over-media {
      grid-row: span 2;
      min-height: 300px;
    }

    .product-card__info {
      align-content: start;
    }
  }
}

.rating {
  align-items: center;
  gap: var(--spacing-1-5);
  display: flex;
}

.rating__stars {
  display: contents;
}

.rating__star {
  color: rgb(var(--star-color));
  position: relative;
  top: -1px;
}

.rating__star--empty {
  filter: grayscale();
}

@media screen and (min-width: 700px) {
  .rating__star {
    width: 15px;
    height: 15px;
  }
}

.rating-with-text {
  gap: var(--spacing-2);
  grid-template-columns: minmax(0, 1fr);
  grid-auto-flow: column;
  align-items: baseline;
  display: grid;
}

.buy-buttons {
  /* align-items: start;
  gap: var(--spacing-2);
  display: grid; */
}

.buy-buttons--compact {
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
}

@media screen and (min-width: 700px) {
  .buy-buttons--multiple {
    gap: var(--spacing-4);
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

.product-card {
  --product-card-badge-spacing: var(--spacing-2);
  --product-card-info-padding: var(--spacing-4);
  --product-card-quick-buy-inset: var(--spacing-2);
  border-radius: var(--rounded-sm);
  isolation: isolate;
  grid-template-columns: minmax(0, 1fr);
  align-content: start;
  align-items: start;
  display: grid;
  position: relative;
}

.product-card__figure {
  display: block;
  position: relative;
}

.product-card__badge-list {
  gap: var(--spacing-2);
  pointer-events: none;
  z-index: 5;
  display: grid;
  position: absolute;
  top: var(--product-card-badge-spacing);
}

.product-card__badge-list:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: var(--product-card-badge-spacing);
}

.product-card__badge-list:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: var(--product-card-badge-spacing);
}

.product-card__image {
  object-fit: contain;
  object-position: center;
  transition: opacity .2s ease-in-out;
}

.product-card__image:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-left-radius: var(--rounded-sm);
  border-top-right-radius: var(--rounded-sm);
}

.product-card__image:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  border-top-right-radius: var(--rounded-sm);
  border-top-left-radius: var(--rounded-sm);
}

.product-card__image--secondary {
  display: none;
}

.product-card__title {
  line-height: 1.5;
}

.product-card__quick-buy {
  position: absolute;
  bottom: var(--product-card-quick-buy-inset);
}

.product-card__quick-buy:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: var(--product-card-quick-buy-inset);
}

.product-card__quick-buy:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: var(--product-card-quick-buy-inset);
}

.product-card__mobile-quick-buy-button {
  background: rgb(var(--background-primary));
  color: rgb(var(--text-primary) / .7);
  border-radius: var(--rounded-button);
  border-width: 1px;
  place-content: center;
  width: 2.25rem;
  height: 2.25rem;
  display: grid;
}

.product-card__mobile-quick-buy-button > .button__loader {
  gap: var(--spacing-1);
}

.product-card__mobile-quick-buy-button > .button__loader > * {
  width: var(--spacing-1);
  height: var(--spacing-1);
}

.product-card__info {
  gap: var(--spacing-1);
  padding: var(--product-card-info-padding);
  grid-template-columns: minmax(0, 1fr);
  justify-items: start;
  display: grid;
}

.product-card--blends .product-card__info {
  padding-block-end: 0;
  padding-inline-start: 0;
  padding-inline-end: 0;
}

.product-card__info--center {
  text-align: center;
  justify-content: center;
  justify-items: center;
}

.product-card__variant-list {
  display: none;
}

@media screen and (min-width: 700px) {
  .product-card {
    --product-card-badge-spacing: var(--spacing-4);
    --product-card-info-padding: var(--spacing-5);
    --product-card-quick-buy-inset: var(--spacing-4);
  }

  .product-card__aside {
    margin-block-start: var(--spacing-1);
  }

  .product-card__variant-list {
    gap: var(--spacing-2);
    margin-block: var(--spacing-2) 2px;
    flex-wrap: wrap;
    display: flex;
  }
}

@media screen and (min-width: 1400px) {
  .product-card__info {
    --product-card-info-padding: var(--spacing-6) var(--spacing-8) var(--spacing-8) var(--spacing-8);
  }
}

@media screen and (pointer: fine) {
  .product-card--show-secondary-media .product-card__figure:hover .product-card__image--primary {
    opacity: 0;
  }

  .product-card--show-secondary-media .product-card__figure:hover .product-card__image--secondary {
    opacity: 1;
  }

  .product-card__image--secondary {
    opacity: 0;
    display: block;
  }

  .product-card__quick-buy {
    opacity: 0;
    visibility: hidden;
    transition: opacity .2s ease-in-out, transform .2s ease-in-out, visibility .2s ease-in-out;
    transform: translateY(5px);
  }

  .product-card:hover .product-card__quick-buy {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.model-viewer {
  position: relative;
}

.model-viewer model-viewer {
  aspect-ratio: 1;
  width: 100%;
  height: 100%;
}

.model-viewer .shopify-model-viewer-ui {
  display: block;
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__controls-area {
  background: rgb(var(--background-primary));
  border-color: rgb(var(--text-primary) / .25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button {
  color: rgb(var(--text-primary));
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:hover {
  color: rgb(var(--text-primary) / .55);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:active, .shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control.focus-visible:focus {
  color: rgb(var(--text-primary) / .55);
  background: rgb(var(--text-primary) / .25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--control:not(:last-child):after {
  border-color: rgb(var(--text-primary) / .25);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster {
  background: rgb(var(--background-primary));
  border-color: rgb(var(--text-primary) / .25);
  border-radius: var(--rounded-button);
}

.shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:hover, .shopify-model-viewer-ui.shopify-model-viewer-ui .shopify-model-viewer-ui__button--poster:focus {
  color: rgb(var(--text-primary) / .55);
}

.price-list {
  align-items: baseline;
  gap: var(--spacing-0-5) var(--spacing-2);
  flex-wrap: wrap;
  display: flex;
}

.price-list--lg {
  gap: var(--spacing-3);
}

.pickup-drawer::part(close-button) {
  align-self: start;
  margin-block-start: .375rem;
}

.pickup-drawer::part(body) {
  padding-block-start: 0;
  padding-block-end: 0;
}

.pickup-availability__closest-location {
  align-items: start;
  gap: var(--spacing-3);
  display: flex;
}

.pickup-availability__media {
  width: var(--spacing-16);
}

.pickup-availability__location {
  gap: var(--spacing-2);
  padding-block-start: var(--spacing-5);
  padding-block-end: var(--spacing-5);
  display: grid;
}

@media screen and (min-width: 700px) {
  .pickup-availability__media {
    width: var(--spacing-20);
  }

  .pickup-availability__location {
    padding-block-start: var(--spacing-6);
    padding-block-end: var(--spacing-6);
  }
}

.quantity-selector {
  height: var(--input-height);
  background: rgb(var(--input-background, transparent));
  color: rgb(var(--input-text-color, var(--text-color)));
  border-radius: var(--rounded-button);
  border-width: 1px;
  display: inline-flex;
}

.quantity-selector__button {
  padding-inline-start: var(--spacing-5);
  padding-inline-end: var(--spacing-5);
}

.quantity-selector__input {
  -webkit-appearance: none;
  appearance: none;
  min-width: var(--spacing-6);
  width: var(--quantity-input-characters-count, 1ch);
  background: inherit;
  text-align: center;
}

.quantity-selector__input:focus {
  outline: none;
}

.quantity-selector__input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quantity-selector__input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

@media screen and (min-width: 700px) {
  .quantity-selector__button {
    padding-inline-start: var(--spacing-6);
    padding-inline-end: var(--spacing-6);
  }
}

.product-quick-add {
  z-index: 2;
  background: transparent;
  visibility: hidden;
  opacity: 0;
  width: 100%;
  padding: 1.25rem;
  transition: visibility .15s ease-in, opacity .15s ease-in, transform .15s ease-in;
  position: fixed;
  bottom: 0;
  transform: translateY(10px);
  display: flex;
  gap: var(--spacing-5);
  align-items: flex-start;
}

.product-quick-add:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  right: 0;
}

.product-quick-add:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  left: 0;
}

.product-quick-add.is-visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

@media screen and (min-width: 700px) {
  .product-quick-add {
    border-radius: var(--rounded-sm);
    background: rgb(var(--dialog-background));
    border-width: 1px;
    width: 35rem;
    padding: 1.5rem;
    bottom: 1rem;
    transform: translateY(0);
  }

  .product-quick-add__image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: var(--rounded-1);
  }

  .product-quick-add__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .product-quick-add__title {
    font-weight: 500;
    font-size: var(--text-base);
  }

  .product-quick-add:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    right: 1rem;
  }

  .product-quick-add:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    left: 1rem;
  }

  .product-quick-add__variant {
    grid-template-columns: 100px minmax(0, 1fr) auto;
    background: rgb(var(--dialog-background));
    align-items: center;
    column-gap: 1.5rem;
    display: grid;
  }

  .product-quick-add__variant.no-image {
    grid-template-columns: minmax(0, 1fr) auto;
  }
}

.quick-buy-drawer {
  --drawer-header-padding: var(--spacing-6);
  --drawer-body-padding: var(--spacing-6);
  --shopify-payment-button-padding: .8125rem var(--spacing-6);
  display: none;
}

.quick-buy-drawer::part(close-button) {
  align-self: start;
  margin-block-start: .25rem;
  margin-inline-end: .25rem;
}

.quick-buy-drawer .variant-picker {
  text-align: center;
}

.quick-buy-drawer .variant-picker__option-info, .quick-buy-drawer .variant-picker__option-values {
  justify-content: center;
}

.quick-buy-drawer .variant-picker__option-values--color {
  max-width: 300px;
  margin-inline-start: auto;
  margin-inline-end: auto;
}

.quick-buy-drawer .block-swatch {
  padding: var(--spacing-2-5) var(--spacing-5);
  height: 2.625rem;
}

.quick-buy-drawer__media {
  max-width: var(--spacing-20);
}

.quick-buy-drawer__info {
  gap: var(--spacing-6);
  margin-block-start: 1rem;
  display: grid;
}

@media screen and (min-width: 700px) {
  .quick-buy-drawer {
    --drawer-header-padding: var(--spacing-6);
    width: 560px;
    height: auto;
  }

  .quick-buy-drawer .variant-picker__option-info, .quick-buy-drawer .block-swatch {
    font-size: var(--text-sm);
  }

  .quick-buy-drawer .color-swatch {
    --swatch-size: 1.5rem;
  }

  .quick-buy-drawer .color-swatch--rectangle {
    --swatch-size: 2rem;
  }

  .quick-buy-drawer :is(.button, .shopify-payment-button__button--unbranded) {
    padding: var(--spacing-3) var(--spacing-6) !important;
    font-size: var(--text-sm) !important;
  }

  .quick-buy-drawer shopify-accelerated-checkout, .quick-buy-drawer shopify-accelerated-checkout-cart {
    --shopify-accelerated-checkout-button-block-size: 46px;
  }

  .quick-buy-drawer__info {
    gap: var(--spacing-8);
  }
}

.variant-picker {
  gap: var(--spacing-6);
  grid-template-columns: minmax(0, 1fr);
  display: grid;
}

.variant-picker .popover {
  --popover-anchor-block-spacing: var(--spacing-2);
  --popover-content-max-width: none;
}

.variant-picker .popover, .variant-picker .popover::part(content) {
  width: 100%;
}

.variant-picker__option-info {
  justify-content: space-between;
  align-items: baseline;
  margin-block-end: var(--spacing-2);
  display: flex;
}

.variant-picker__option-values {
  display: flex;
}

.variant-picker__option-values.scroll-area {
  padding-block-start: 1px;
  padding-block-end: 1px;
}

@media screen and (min-width: 700px) {
  .variant-picker__option-values {
    flex-wrap: wrap;
  }
}

@media (scripting: none) {
  .variant-picker__option {
    display: none;
  }
}

.prose ol, .list-decimal {
  list-style: decimal inside;
}

.prose ul, .list-disc {
  list-style: inside;
}

.prose :is(ol, ul) :is(ol, ul) {
  margin-block-start: 1em;
  margin-inline-start: 1em;
}

.prose > :first-child, .prose > :first-child :first-child {
  margin-block-start: 0 !important;
}

.prose > :last-child, .prose > :last-child :last-child {
  margin-block-end: 0 !important;
}

.prose * + :is(p, div, ul, ol) {
  margin-block-start: var(--spacing-3);
}

.prose * + :is(.h0, .h1, .h2, h1, h2) {
  margin-block-start: var(--spacing-4);
}

.prose * + :is(.h3, .h4, .h5, .h6, h3, h4, h5, h6) {
  margin-block-start: var(--spacing-3);
}

.prose * + .button {
  margin-block-start: var(--spacing-6) !important;
}

.prose * + span:not(:empty, .metafield-multi_line_text_field) {
  display: inline-block;
}

.prose :is(.h0, .h1, .h2, .h3, .h4, h1, h2, h3, h4) + * {
  margin-block-start: var(--spacing-5);
}

.prose :is(.h5, .h6, h5, h6) + * {
  margin-block-start: var(--spacing-4);
}

.prose :is(p img:not([style*="float"]):only-child, div img:not([style*="float"]):only-child, figure, video) {
  margin-block: var(--spacing-8);
}

.prose figcaption {
  font-style: italic;
  font-size: var(--text-sm);
  color: rgb(var(--text-color) / .7);
  margin-block-start: .5em;
}

.prose ul, .prose ol {
  row-gap: .6em;
  display: grid;
}

.prose br {
  margin-block-start: 0 !important;
  margin-block-end: 0 !important;
}

.prose :is(iframe[src*="youtube"], iframe[src*="youtu.be"], iframe[src*="vimeo"]) {
  aspect-ratio: 16 / 9;
  width: 100%;
  height: auto;
}

@media screen and (min-width: 700px) {
  .prose * + :is(p, div, ul, ol) {
    margin-block-start: var(--spacing-4);
  }

  .prose * + :is(.h0, .h1, .h2, h1, h2) {
    margin-block-start: var(--spacing-6);
  }

  .prose * + :is(.h3, .h4, h3, h4) {
    margin-block-start: var(--spacing-5);
  }

  .prose * + :is(.h5, .h6, h5, h6) {
    margin-block-start: var(--spacing-4);
  }

  .prose * + .button {
    margin-block-start: var(--spacing-8) !important;
  }

  .prose .h0 + .button {
    margin-block-start: var(--spacing-10) !important;
  }

  .prose :is(.h0, .h1, .h2, .h3, h1, h2, h3) + * {
    margin-block-start: var(--spacing-6);
  }

  .prose :is(.h4, h4) + * {
    margin-block-start: var(--spacing-5);
  }

  .prose :is(.h5, .h6, h5, h6) + * {
    margin-block-start: var(--spacing-4);
  }

  .prose :is(p img:not([style*="float"]):only-child, div img:not([style*="float"]):only-child, figure, video) {
    margin-block: var(--spacing-12);
  }
}

@media screen and (min-width: 1150px) {
  .prose * + p {
    margin-block-start: var(--spacing-6);
  }

  .prose * + :is(.h0, .h1, h1) {
    margin-block-start: var(--spacing-8);
  }

  .prose * + :is(.h2, .h3, .h4, h2, h3, h4) {
    margin-block-start: var(--spacing-6);
  }

  .prose * + :is(.h5, .h6, h5, h6) {
    margin-block-start: var(--spacing-4);
  }

  .prose :is(.h0, .h1, .h2, h1, h2) + * {
    margin-block-start: var(--spacing-8);
  }

  .prose :is(.h3, .h4, h3, h4) + * {
    margin-block-start: var(--spacing-6);
  }

  .prose :is(.h5, .h6, h5, h6) + * {
    margin-block-start: var(--spacing-4);
  }
}

.link, .prose a:not(.button) {
  text-underline: none;
  background: linear-gradient(to right, currentColor, currentColor) 0 min(100%, 1.35em) / 100% 1px no-repeat;
  transition: background-size .3s ease-in-out, color .3s ease-in-out;
}

@supports (height: 1lh) {
  :is(.link, .prose a:not(.button)) {
    background: linear-gradient(to right, currentColor, currentColor) 0 min(100%, 1.2lh) / 100% 1px no-repeat;
  }
}

@media screen and (pointer: fine) {
  :is(.link, .prose a:not(.button)):hover {
    background-size: 0 1px;
  }

  .text-subdued :is(.link, .prose a:not(.button)):hover {
    color: rgb(var(--text-color));
  }
}

.reversed-link {
  text-underline: none;
  background: linear-gradient(to right, currentColor, currentColor) 0 min(100%, 1.35em) / 0 1px no-repeat;
  transition: background-size .3s ease-in-out;
}

@supports (height: 1lh) {
  .reversed-link {
    background: linear-gradient(to right, currentColor, currentColor) 0 min(100%, 1.2lh) / 0 1px no-repeat;
  }
}

@media screen and (pointer: fine) {
  .group:hover .reversed-link, .reversed-link.hover\:show:hover {
    background-size: 100% 1px;
  }
}

.link-faded {
  opacity: .7;
  transition: opacity .2s ease-in-out;
}

@media screen and (pointer: fine) {
  .link-faded:hover {
    opacity: 1;
  }
}

.link-faded-reverse {
  transition: opacity .2s ease-in-out;
}

@media screen and (pointer: fine) {
  .link-faded-reverse:hover {
    opacity: .7;
  }
}

table {
  width: 100%;
}

table caption {
  text-align: inherit;
  margin-block-end: .5rem;
}

th {
  text-align: start;
}

th, td {
  padding: var(--spacing-4);
  vertical-align: top;
}

table:not(.table--bordered) :is(th, td):first-child {
  padding-inline-start: 0;
}

table:not(.table--bordered) :is(th, td):last-child {
  padding-inline-end: 0;
}

thead th {
  border-block-end-width: 1px;
}

tbody tr + tr > *, thead + tbody tr > * {
  border-top-width: 1px;
}

tfoot td {
  border-top-width: 1px;
  padding-block-start: var(--spacing-6);
  padding-block-end: 0;
}

.table-row-hover {
  cursor: pointer;
  transition: background .2s ease-in-out;
}

.table--bordered tr {
  border-width: 1px;
}

.table--sm td {
  padding: var(--spacing-2);
}

@media screen and (pointer: fine) {
  .table-row-hover:hover {
    background: rgb(var(--text-color) / .05);
  }
}

@media screen and (min-width: 700px) {
  th, td {
    padding-block-start: var(--spacing-6);
    padding-block-end: var(--spacing-6);
  }

  tfoot td {
    padding-block-start: var(--spacing-8);
  }
}

.blockquote, .prose blockquote {
  --quote-width: 51px;
  --quote-height: 37px;
  --quote-inset-block-start: calc(var(--spacing-5) * -1);
  --quote-inset-inline-start: var(--spacing-1);
  margin-inline-start: 0;
  margin-inline-end: 0;
  font-weight: bold;
  position: relative;
}

:is(.blockquote, .prose blockquote):before {
  content: "";
  width: var(--quote-width);
  height: var(--quote-height);
  background: rgb(var(--text-color) / .1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg width='86' height='55' viewBox='0 0 86 55' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M71.3163 54.6H42.5163L60.3163 0.400024H85.5163L71.3163 54.6ZM29.3163 54.6H0.716309L18.9163 0.400024H44.1163L29.3163 54.6Z' fill='%23252627'/%3E%3C/svg%3E%0A");
  mask-image: url("data:image/svg+xml,%3Csvg width='86' height='55' viewBox='0 0 86 55' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M71.3163 54.6H42.5163L60.3163 0.400024H85.5163L71.3163 54.6ZM29.3163 54.6H0.716309L18.9163 0.400024H44.1163L29.3163 54.6Z' fill='%23252627'/%3E%3C/svg%3E%0A");
  -webkit-mask-size: var(--quote-width) var(--quote-height);
  mask-size: var(--quote-width) var(--quote-height);
  position: absolute;
  top: var(--quote-inset-block-start);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
}

:is(.blockquote, .prose blockquote):not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: var(--quote-inset-inline-start);
}

:is(.blockquote, .prose blockquote):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: var(--quote-inset-inline-start);
}

.blockquote {
  margin-block-start: var(--spacing-5);
}

.prose blockquote {
  --quote-inset-block-start: calc(var(--spacing-4) * -1);
  --quote-inset-inline-start: calc(50% - (var(--quote-width) / 2));
  text-align: center;
  font-size: var(--text-h3);
  margin-block-start: var(--spacing-14);
  margin-block-end: var(--spacing-10);
  line-height: 1.2;
}

@media screen and (min-width: 700px) {
  .blockquote, .prose blockquote {
    --quote-width: 70px;
    --quote-height: 45px;
  }

  .blockquote {
    --quote-inset-block-start: calc(var(--spacing-6) * -1);
    margin-block-start: var(--spacing-8);
  }
}

@media screen and (min-width: 1150px) {
  .prose blockquote {
    --quote-width: 85px;
    --quote-height: 55px;
    --quote-inset-block-start: calc(var(--spacing-5) * -1);
    margin-block-start: var(--spacing-18);
    margin-block-end: var(--spacing-12);
  }

  .blockquote {
    --quote-inset-inline-start: var(--spacing-2-5);
  }
}

.shape-circle, .shape-square, .shape-diamond {
  --size: var(--spacing-1-5);
  width: var(--size);
  height: var(--size);
  background: currentColor;
  display: block;
}

.shape-circle {
  border-radius: var(--rounded-full);
}

.shape-diamond {
  transform: rotateZ(45deg);
}

.shape-line {
  background: rgb(var(--text-color) / .1);
  width: 1px;
  height: 100%;
}

.shape--sm {
  --size: var(--spacing-1);
}

.shape--lg {
  --size: var(--spacing-2);
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.skeleton {
  background: rgb(var(--text-color) / .15);
  border-radius: 4px;
  flex-shrink: 0;
  animation: 2.5s cubic-bezier(.4, 0, .6, 1) infinite pulse;
  display: block;
}

.skeleton--tab {
  width: 88px;
  height: 20px;
}

.skeleton--thumbnail {
  width: 80px;
  height: 80px;
}

.skeleton--text {
  width: var(--skeleton-text-width, 100%);
  height: 12px;
}

@media screen and (min-width: 700px) {
  .skeleton--tab {
    width: 148px;
    height: 34px;
  }

  .skeleton--thumbnail {
    width: 96px;
    height: 96px;
  }
}

.color-swatch {
  --swatch-offset: 3px;
  --swatch-size: var(--spacing-7);
  --swatch-margin: calc(var(--swatch-offset) * 2);
  width: var(--swatch-size);
  height: var(--swatch-size);
  margin: var(--swatch-margin);
  background: var(--swatch-background) center / cover;
  flex-shrink: 0;
  place-items: center;
  display: grid;
  position: relative;
}

.color-swatch.is-disabled {
  background: linear-gradient(to bottom right, transparent calc(50% - 1px), rgb(var(--background)) calc(50% - 1px) calc(50% + 1px), transparent calc(50% + 1px)), var(--swatch-background) center / cover;
}

:disabled + .color-swatch {
  display: none;
}

.color-swatch:before {
  content: "";
  inset: calc(-1 * var(--swatch-offset));
  opacity: 0;
  border-radius: inherit;
  will-change: transform;
  transition: opacity .2s ease-in-out, transform .2s ease-in-out;
  position: absolute;
  transform: scale(.75);
  box-shadow: 0 0 0 2px;
}

:checked + .color-swatch:before, .color-swatch.is-selected:before {
  opacity: 1;
  transform: scale(1);
}

:focus-visible + .color-swatch {
  outline-offset: calc(2px + var(--swatch-offset) * 2);
}

.color-swatch--rectangle {
  --swatch-size: var(--spacing-10);
  height: calc(var(--swatch-size) * .4);
  margin: 0;
}

.color-swatch--rectangle:before {
  width: 100%;
  bottom: -3px;
  box-shadow: 0 2px;
}

.color-swatch--rectangle:not(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  left: 0;
}

.color-swatch--rectangle:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)):before {
  right: 0;
}

.color-swatch--sm {
  --swatch-size: var(--spacing-3-5);
  --swatch-offset: 2px;
}

.color-swatch--rectangle.color-swatch--sm {
  --swatch-size: var(--spacing-5);
  margin-block-start: 7px;
  margin-block-end: 6px;
}

.color-swatch--rectangle.color-swatch--sm:before {
  bottom: -2px;
}

.color-swatch__view-more {
  padding: 1px var(--spacing-2);
  color: rgb(var(--text-color) / .7);
  border-width: 1px;
  margin-inline-start: var(--spacing-1-5);
}

.thumbnail-swatch {
  --swatch-size: 60px;
  width: var(--swatch-size);
  height: var(--swatch-size);
  border-radius: min(4px, var(--rounded-input));
  flex-shrink: 0;
  display: block;
  position: relative;
}

.thumbnail-swatch:before {
  content: "";
  border-radius: inherit;
  opacity: 0;
  will-change: transform;
  transition: opacity .2s ease-in-out, transform .2s ease-in-out;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  transform: scale(.95);
  box-shadow: 0 0 0 2px;
}

:disabled + .thumbnail-swatch {
  display: none;
}

.thumbnail-swatch.is-disabled:after {
  content: "";
  background-image: linear-gradient(to bottom right, transparent calc(50% - 1px), rgb(var(--text-color) / .5) calc(50% - 1px) calc(50% + 1px), transparent calc(50% + 1px));
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}


.v-stack.gap-4 {
    padding-top: 40px;
}
.product-info__buy-buttons .button, .btn {
  padding: 8px 20px !important;
}