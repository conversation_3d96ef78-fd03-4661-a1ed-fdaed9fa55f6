
{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}
<div class="new-card-slider section-{{ section.id }}-padding">
  <div class="section section-blends section-full text-custom">
    <div class="new-card-slider-main">
      {% if section.settings.section_title != blank %}
      <div class="section-title">
        <h2>{{ section.settings.section_title }}</h2>
      </div>
      {% endif %}
      <div class="sub-title">
      {% if section.settings.section_description != blank %}
      <div class="section-description">
        {{ section.settings.section_description }}
      </div>
      {% endif %}
      </div>
      <div class="new-card-slider-content">
     
       <div class="new-card-carousel">
        
       {% for block in section.blocks %}
          <div class="carousel-item">
            <div class="block-top">
             {% if block.settings.card_title != blank %}
              <div class="card-title">
                <h2>{{ block.settings.card_title }}</h2>
              </div>
              {% endif %}
             {% if block.settings.card_subtitle != blank %}
              <div class="card-subtitle">
                <h2>{{ block.settings.card_subtitle }}</h2>
              </div>
              {% endif %}
            </div>
           <div class="block-bottom">
             {% if block.settings.card_description != blank %}
              <div class="card-description">
                {{ block.settings.card_description }}
              </div>
              {% endif %}
              {% if block.settings.card_purity != blank %}
              <div class="card-purity">
                {{  block.settings.card_purity }}
              </div>
              {% endif %}
           </div>
          </div>
        {% endfor %}
        
        </div>
      </div>
    </div>
  </div>
</div>
<script>
$(document).ready(function(){
  $('.new-card-carousel').flickity({
    // options
    cellAlign: 'left',
    contain: true,
    imagesLoaded: true,
    pageDots: false
  });

});
</script>
<style>
.new-card-slider .carousel-item {
  width: 45%;
  padding: 10px;
  border: 2px solid #241F23;
  border-radius: 10px;
  margin-right: 10px !important;
}
.new-card-slider .card-image img {
    display: block;
    width: 100%;
    height: 100%;
}
.new-card-slider .section-title {
  padding-bottom: 20px;
}
.new-card-slider .section-title h2 {
  font-family: var(--heading-font-family);
  font-weight: 700;
  font-size: 62px;
  line-height: 100%;
  letter-spacing: 0px;
  vertical-align: middle;
  text-transform: uppercase;

}
.new-card-slider .sub-title {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
}
.new-card-slider .section-description {
    max-width: 50%;
    display: flex;
    column-gap: 10px;
}
.new-card-slider .section-description::before {
  content: url(https://cdn.shopify.com/s/files/1/0878/9170/6174/files/Vector.svg?v=1750574682);
}
.new-card-slider .new-card-slider-content {
    padding-top: 30px;
}
.new-card-slider .card-title h2 {
    padding-top: 20px;
    font-family: var(--heading-font-family);
    font-weight: 800;
    font-size: 90px;
    line-height: 110%;
    letter-spacing: .4px;
    vertical-align: middle;
    text-transform: uppercase;
   
  
}
.new-card-slider .card-subtitle h2 {
    padding-top: 20px;
    font-family: var(--heading-font-family);
    font-weight: 800;
    font-size: 60px;
    line-height: 110%;
    letter-spacing: .4px;
    vertical-align: middle;
    text-transform: uppercase;
    
}
.new-card-slider .section-description p,
.new-card-slider .card-description p {
    font-family: Meltmino;
    font-weight: 400;
    font-size: 20px;
    line-height: 23.4px;
    letter-spacing: 0px;
    vertical-align: middle;
    text-transform: uppercase;
    color: #2A2A2A;
}
.new-card-slider .block-top {
    text-align: center;
    padding-bottom: 60px;
}
.new-card-slider .block-bottom {
    display: flex;
    /* justify-content: space-between; */
    align-items: center;
}
.new-card-slider .card-purity{
  width: 30%;
  text-align: end;
}
.new-card-slider .card-purity p {
    font-weight: 700;
    font-size: 20px;
    line-height: 124%;
    letter-spacing: .4px;
    vertical-align: middle;
    text-transform: uppercase;
}
.new-card-slider .card-description {
    width: 70%;
}

@media only screen and (min-width: 769px) and (max-width: 1024px) {
   .new-card-slider .carousel-item {
    width: 60%;
   }
  .new-card-slider .section-description p, 
  .new-card-slider .card-description p {
    font-size: 18px;
  }
}
 @media only screen and (min-width: 481px) and (max-width: 768px) {
   
   .new-card-slider .section-title h2 {
     font-size: 45px;
   }
   .new-card-slider .carousel-item {
    width: 100%;
   }
   .new-card-slider .section-description {
    max-width: 100%;
   }
 } 
 @media only screen and (max-width: 480px) {
   .new-card-slider .section-title h2 {
     font-size: 30px;
   }
   .new-card-slider .section-description p, 
   .new-card-slider .card-description p {
     font-size: 12px;
   }
   .new-card-slider .card-title h2 {
     font-size: 60px;
   }
   .new-card-slider .card-subtitle h2 {
     font-size: 40px;
   }
   .new-card-slider .carousel-item {
    width: 100%;
   }
   .new-card-slider .section-description {
    max-width: 100%;
   }
   .new-card-slider .block-bottom {
    flex-direction: column-reverse;
    align-items: flex-start;
  }
   .new-card-slider .card-purity {
    width: 70%;
    text-align: start;
    padding-bottom: 10px;
}
 }
</style>
{% schema %}
  {
    "name": "New Card Slider",
    "settings": [
    {
      "type": "text",
      "id": "section_title",
      "label": "Heading"
    },
    {
      "type": "richtext",
      "id": "section_description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Section Padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding Top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Padding Bottom",
      "default": 52
    }
    ],
    "blocks": [
    {
      "type": "card-block",
      "name": "Card Block",
      "limit": 12,
      "settings": [
        {
          "type": "text",
          "id": "card_title",
          "label": "Heading"
        },
        {
          "type": "text",
          "id": "card_subtitle",
          "label": "Sub Heading"
        },
        {
          "type": "richtext",
          "id": "card_description",
          "label": "Description"
        },
        {
          "type": "richtext",
          "id": "card_purity",
          "label": "purity"
        }
      ]
    }
    ],
    "presets": [
    {
      "name": "New Card Slider",
      "blocks": [
      {
        "type": "card-block"
      }
      ]
    }
  ]
  }
{% endschema %}